import React, { createContext, useContext, useEffect, useState } from 'react';

export type Theme = 'light' | 'dark' | 'professional' | 'auto';

interface ThemeContextType {
  theme: Theme;
  actualTheme: 'light' | 'dark' | 'professional';
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  availableThemes: { value: Theme; label: string; description: string }[];
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_STORAGE_KEY = 'ai-doc-processor-theme';

const availableThemes = [
  {
    value: 'auto' as Theme,
    label: 'Auto',
    description: 'Follow system preference',
  },
  {
    value: 'light' as Theme,
    label: 'Light',
    description: 'Light theme for better readability',
  },
  {
    value: 'dark' as Theme,
    label: 'Dark',
    description: 'Dark theme for reduced eye strain',
  },
  {
    value: 'professional' as Theme,
    label: 'Professional',
    description: 'Professional theme for business use',
  },
];

/**
 * Theme provider component that manages application theming
 */
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>('auto');
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');

  // Determine the actual theme to apply
  const actualTheme =
    theme === 'auto' ? systemTheme : theme === 'professional' ? 'professional' : theme;

  // Load theme from storage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY) as Theme;
    if (savedTheme && availableThemes.some(t => t.value === savedTheme)) {
      setThemeState(savedTheme);
    }

    // Detect system theme preference
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');

    // Listen for system theme changes
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, []);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;

    // Remove all theme classes
    root.classList.remove('light', 'dark', 'professional');
    root.removeAttribute('data-theme');

    // Apply the actual theme
    root.classList.add(actualTheme);
    root.setAttribute('data-theme', actualTheme);

    // Log theme change
    if (window.electronAPI?.logToMain) {
      window.electronAPI.logToMain('info', 'Theme Changed', {
        theme,
        actualTheme,
        systemTheme,
        timestamp: new Date().toISOString(),
      });
    }
  }, [theme, actualTheme, systemTheme]);

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    localStorage.setItem(THEME_STORAGE_KEY, newTheme);
  };

  const toggleTheme = () => {
    const currentIndex = availableThemes.findIndex(t => t.value === theme);
    const nextIndex = (currentIndex + 1) % availableThemes.length;
    const nextTheme = availableThemes[nextIndex];
    if (nextTheme) {
      setTheme(nextTheme.value);
    }
  };

  const value: ThemeContextType = {
    theme,
    actualTheme,
    setTheme,
    toggleTheme,
    availableThemes,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};

/**
 * Hook to use theme context
 */
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

/**
 * Theme toggle button component
 */
export const ThemeToggle: React.FC<{ className?: string }> = ({ className = '' }) => {
  const { theme, actualTheme, toggleTheme, availableThemes } = useTheme();

  const currentTheme = availableThemes.find(t => t.value === theme);

  return (
    <button
      className={`btn btn-ghost btn-sm ${className}`}
      onClick={toggleTheme}
      title={`Current theme: ${currentTheme?.label} - Click to cycle themes`}
      aria-label='Toggle theme'
    >
      {actualTheme === 'dark' ? (
        <svg className='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z'
          />
        </svg>
      ) : (
        <svg className='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z'
          />
        </svg>
      )}
      <span className='ml-1 text-xs'>{currentTheme?.label}</span>
    </button>
  );
};

/**
 * Theme selector dropdown component
 */
export const ThemeSelector: React.FC<{ className?: string }> = ({ className = '' }) => {
  const { theme, setTheme, availableThemes } = useTheme();

  return (
    <div className={`dropdown dropdown-end ${className}`}>
      <label tabIndex={0} className='btn btn-ghost btn-sm'>
        <svg className='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z'
          />
        </svg>
        Theme
      </label>
      <ul
        tabIndex={0}
        className='dropdown-content menu p-2 shadow bg-base-100 rounded-box w-64 mt-2'
      >
        {availableThemes.map(themeOption => (
          <li key={themeOption.value}>
            <button
              className={`text-left ${theme === themeOption.value ? 'active' : ''}`}
              onClick={() => setTheme(themeOption.value)}
            >
              <div>
                <div className='font-medium'>{themeOption.label}</div>
                <div className='text-xs text-base-content/70'>{themeOption.description}</div>
              </div>
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ThemeProvider;
