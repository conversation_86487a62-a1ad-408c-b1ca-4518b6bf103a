import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { v4 as uuid } from 'uuid';
import {
  ModalState,
  ToastMessage,
  ToastType,
  NotificationOptions,
  NotificationPriority,
  KeyboardShortcut,
  ShortcutContext,
  PanelLayoutState,
  SidebarLayoutState,
  SidebarPosition,
  WindowBounds,
  ColorScheme,
  UIDensity,
  ToastState,
  NotificationState,
} from '../../shared/types/UI';

export interface UIState {
  // Window and layout state
  windowBounds: WindowBounds;
  isMaximized: boolean;
  isMinimized: boolean;
  isFullscreen: boolean;

  // Panel management
  panels: PanelLayoutState[];
  sidebar: SidebarLayoutState;

  // Modal management
  modals: ModalState[];
  activeModalId: string | null;
  modalZIndex: number;

  // Toast and notification management
  toasts: ToastMessage[];
  notifications: NotificationOptions[];
  maxToasts: number;
  maxNotifications: number;

  // Keyboard shortcuts
  shortcuts: KeyboardShortcut[];
  activeShortcuts: Map<string, KeyboardShortcut>;
  shortcutContext: ShortcutContext;

  // Theme and appearance
  colorScheme: ColorScheme;
  density: UIDensity;
  fontSize: number;
  animations: boolean;
  transparency: boolean;

  // Loading and progress states
  globalLoading: boolean;
  loadingMessage: string;
  progressValue: number;
  progressMax: number;

  // Focus and interaction state
  focusedElement: string | null;
  dragState: DragState | null;
  contextMenuVisible: boolean;
  contextMenuPosition: { x: number; y: number } | null;

  // Error and status
  globalError: string | null;
  statusMessage: string | null;
  statusType: 'info' | 'success' | 'warning' | 'error' | null;

  // Actions
  // Window management
  setWindowBounds: (bounds: WindowBounds) => void;
  setMaximized: (maximized: boolean) => void;
  setMinimized: (minimized: boolean) => void;
  setFullscreen: (fullscreen: boolean) => void;

  // Panel management
  addPanel: (panel: PanelLayoutState) => void;
  removePanel: (panelId: string) => void;
  updatePanel: (panelId: string, updates: Partial<PanelLayoutState>) => void;
  togglePanelVisibility: (panelId: string) => void;
  resizePanel: (panelId: string, size: { width?: number; height?: number }) => void;

  // Sidebar management
  updateSidebar: (updates: Partial<SidebarLayoutState>) => void;
  toggleSidebar: () => void;
  setSidebarWidth: (width: number) => void;

  // Modal management
  openModal: (modal: Omit<ModalState, 'id' | 'zIndex'>) => string;
  closeModal: (modalId: string) => void;
  closeAllModals: () => void;
  updateModal: (modalId: string, updates: Partial<ModalState>) => void;

  // Toast management
  showToast: (toast: Omit<ToastMessage, 'id' | 'timestamp'>) => string;
  hideToast: (toastId: string) => void;
  clearAllToasts: () => void;
  updateToast: (toastId: string, updates: Partial<ToastMessage>) => void;

  // Notification management
  showNotification: (notification: NotificationOptions) => string;
  hideNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;

  // Keyboard shortcuts
  registerShortcut: (shortcut: KeyboardShortcut) => void;
  unregisterShortcut: (shortcutId: string) => void;
  setShortcutContext: (context: ShortcutContext) => void;
  triggerShortcut: (keys: string[]) => boolean;

  // Theme and appearance
  setColorScheme: (scheme: ColorScheme) => void;
  setDensity: (density: UIDensity) => void;
  setFontSize: (size: number) => void;
  setAnimations: (enabled: boolean) => void;
  setTransparency: (enabled: boolean) => void;

  // Loading and progress
  setGlobalLoading: (loading: boolean, message?: string) => void;
  setProgress: (value: number, max?: number) => void;

  // Focus and interaction
  setFocusedElement: (elementId: string | null) => void;
  setDragState: (state: DragState | null) => void;
  showContextMenu: (position: { x: number; y: number }) => void;
  hideContextMenu: () => void;

  // Error and status
  setGlobalError: (error: string | null) => void;
  setStatusMessage: (
    message: string | null,
    type?: 'info' | 'success' | 'warning' | 'error'
  ) => void;
  clearStatus: () => void;

  // Utility actions
  reset: () => void;
  getPanel: (panelId: string) => PanelLayoutState | undefined;
  getModal: (modalId: string) => ModalState | undefined;
  getToast: (toastId: string) => ToastMessage | undefined;
}

export interface DragState {
  type: 'panel' | 'tab' | 'file' | 'custom';
  data: any;
  startPosition: { x: number; y: number };
  currentPosition: { x: number; y: number };
  isDragging: boolean;
}

const initialWindowBounds: WindowBounds = {
  x: 100,
  y: 100,
  width: 1200,
  height: 800,
};

const initialSidebar: SidebarLayoutState = {
  position: SidebarPosition.LEFT,
  width: 300,
  isVisible: true,
  isCollapsed: false,
  activePanel: 'file-explorer',
  panels: ['file-explorer', 'knowledge-base', 'timeline'],
};

const initialState = {
  // Window and layout state
  windowBounds: initialWindowBounds,
  isMaximized: false,
  isMinimized: false,
  isFullscreen: false,

  // Panel management
  panels: [],
  sidebar: initialSidebar,

  // Modal management
  modals: [],
  activeModalId: null,
  modalZIndex: 1000,

  // Toast and notification management
  toasts: [],
  notifications: [],
  maxToasts: 5,
  maxNotifications: 10,

  // Keyboard shortcuts
  shortcuts: [],
  activeShortcuts: new Map<string, KeyboardShortcut>(),
  shortcutContext: ShortcutContext.GLOBAL,

  // Theme and appearance
  colorScheme: ColorScheme.AUTO,
  density: UIDensity.NORMAL,
  fontSize: 14,
  animations: true,
  transparency: false,

  // Loading and progress states
  globalLoading: false,
  loadingMessage: '',
  progressValue: 0,
  progressMax: 100,

  // Focus and interaction state
  focusedElement: null,
  dragState: null,
  contextMenuVisible: false,
  contextMenuPosition: null,

  // Error and status
  globalError: null,
  statusMessage: null,
  statusType: null,
};

export const useUIStore: any = create<UIState>()(
  subscribeWithSelector(
    immer<UIState>((set, _get) => ({
      ...initialState,

      // Window management
      setWindowBounds: (bounds: WindowBounds) =>
        set(state => {
          state.windowBounds = bounds;
        }),

      setMaximized: (maximized: boolean) =>
        set(state => {
          state.isMaximized = maximized;
        }),

      setMinimized: (minimized: boolean) =>
        set(state => {
          state.isMinimized = minimized;
        }),

      setFullscreen: (fullscreen: boolean) =>
        set(state => {
          state.isFullscreen = fullscreen;
        }),

      // Panel management
      addPanel: (panel: PanelLayoutState) =>
        set(state => {
          state.panels.push(panel);
        }),

      removePanel: (panelId: string) =>
        set(state => {
          state.panels = state.panels.filter((p: PanelLayoutState) => p.id !== panelId);
        }),

      updatePanel: (panelId: string, updates: Partial<PanelLayoutState>) =>
        set(state => {
          const panel = state.panels.find((p: PanelLayoutState) => p.id === panelId);
          if (panel) {
            Object.assign(panel, updates);
          }
        }),

      togglePanelVisibility: (panelId: string) =>
        set(state => {
          const panel = state.panels.find((p: PanelLayoutState) => p.id === panelId);
          if (panel) {
            panel.isVisible = !panel.isVisible;
          }
        }),

      resizePanel: (panelId: string, size: { width?: number; height?: number }) =>
        set(state => {
          const panel = state.panels.find((p: PanelLayoutState) => p.id === panelId);
          if (panel) {
            if (size.width !== undefined) {
              panel.size.width = size.width;
            }
            if (size.height !== undefined) {
              panel.size.height = size.height;
            }
          }
        }),

      // Sidebar management
      updateSidebar: (updates: Partial<SidebarLayoutState>) =>
        set(state => {
          state.sidebar = { ...state.sidebar, ...updates };
        }),

      toggleSidebar: () =>
        set(state => {
          state.sidebar.isVisible = !state.sidebar.isVisible;
        }),

      setSidebarWidth: (width: number) =>
        set(state => {
          state.sidebar.width = Math.max(200, Math.min(600, width));
        }),

      // Modal management
      openModal: (modalData: Omit<ModalState, 'id' | 'zIndex'>) => {
        const modalId = uuid();

        set(state => {
          const modal: ModalState = {
            ...modalData,
            id: modalId,
            zIndex: state.modalZIndex + state.modals.length,
          };

          state.modals.push(modal);
          state.activeModalId = modalId;
        });

        return modalId;
      },

      closeModal: (modalId: string) =>
        set(state => {
          state.modals = state.modals.filter((m: ModalState) => m.id !== modalId);

          if (state.activeModalId === modalId) {
            const topModal = state.modals[state.modals.length - 1];
            state.activeModalId = topModal?.id || null;
          }
        }),

      closeAllModals: () =>
        set(state => {
          state.modals = [];
          state.activeModalId = null;
        }),

      updateModal: (modalId: string, updates: Partial<ModalState>) =>
        set(state => {
          const modal = state.modals.find((m: ModalState) => m.id === modalId);
          if (modal) {
            Object.assign(modal, updates);
          }
        }),

      // Toast management
      showToast: (toastData: Omit<ToastMessage, 'id' | 'timestamp'>) => {
        const toastId = uuid();

        set(state => {
          const toast: ToastMessage = {
            ...toastData,
            id: toastId,
            timestamp: new Date(),
          };

          state.toasts.push(toast);

          // Remove oldest toasts if exceeding max
          if (state.toasts.length > state.maxToasts) {
            state.toasts = state.toasts.slice(-state.maxToasts);
          }
        });

        return toastId;
      },

      hideToast: (toastId: string) =>
        set(state => {
          state.toasts = state.toasts.filter((t: ToastState) => t.id !== toastId);
        }),

      clearAllToasts: () =>
        set(state => {
          state.toasts = [];
        }),

      updateToast: (toastId: string, updates: Partial<ToastMessage>) =>
        set(state => {
          const toast = state.toasts.find((t: ToastState) => t.id === toastId);
          if (toast) {
            Object.assign(toast, updates);
          }
        }),

      // Notification management
      showNotification: (notificationData: NotificationOptions) => {
        const notificationId = notificationData.id || uuid();

        set(state => {
          const notification: NotificationOptions = {
            ...notificationData,
            id: notificationId,
          };

          state.notifications.push(notification);

          // Remove oldest notifications if exceeding max
          if (state.notifications.length > state.maxNotifications) {
            state.notifications = state.notifications.slice(-state.maxNotifications);
          }
        });

        return notificationId;
      },

      hideNotification: (notificationId: string) =>
        set(state => {
          state.notifications = state.notifications.filter(
            (n: NotificationState) => n.id !== notificationId
          );
        }),

      clearAllNotifications: () =>
        set(state => {
          state.notifications = [];
        }),

      // Keyboard shortcuts
      registerShortcut: (shortcut: KeyboardShortcut) =>
        set(state => {
          state.shortcuts.push(shortcut);
          state.activeShortcuts.set(shortcut.keys.join('+'), shortcut);
        }),

      unregisterShortcut: (shortcutId: string) =>
        set(state => {
          const shortcut = state.shortcuts.find((s: KeyboardShortcut) => s.id === shortcutId);
          if (shortcut) {
            state.shortcuts = state.shortcuts.filter((s: KeyboardShortcut) => s.id !== shortcutId);
            state.activeShortcuts.delete(shortcut.keys.join('+'));
          }
        }),

      setShortcutContext: (context: ShortcutContext) =>
        set(state => {
          state.shortcutContext = context;
        }),

      triggerShortcut: (keys: string[]) => {
        const state = useUIStore.getState();
        const keyCombo = keys.join('+');
        const shortcut = state.activeShortcuts.get(keyCombo);

        if (
          shortcut &&
          (shortcut.context === state.shortcutContext ||
            shortcut.context === ShortcutContext.GLOBAL)
        ) {
          try {
            shortcut.action();
            return true;
          } catch (error) {
            console.error('Error executing shortcut:', error);
            return false;
          }
        }

        return false;
      },

      // Theme and appearance
      setColorScheme: (scheme: ColorScheme) =>
        set(state => {
          state.colorScheme = scheme;
        }),

      setDensity: (density: UIDensity) =>
        set(state => {
          state.density = density;
        }),

      setFontSize: (size: number) =>
        set(state => {
          state.fontSize = Math.max(10, Math.min(24, size));
        }),

      setAnimations: (enabled: boolean) =>
        set(state => {
          state.animations = enabled;
        }),

      setTransparency: (enabled: boolean) =>
        set(state => {
          state.transparency = enabled;
        }),

      // Loading and progress
      setGlobalLoading: (loading: boolean, message?: string) =>
        set(state => {
          state.globalLoading = loading;
          state.loadingMessage = message || '';
        }),

      setProgress: (value: number, max?: number) =>
        set(state => {
          state.progressValue = Math.max(0, Math.min(max || state.progressMax, value));
          if (max !== undefined) {
            state.progressMax = max;
          }
        }),

      // Focus and interaction
      setFocusedElement: (elementId: string | null) =>
        set(state => {
          state.focusedElement = elementId;
        }),

      setDragState: (dragState: DragState | null) =>
        set(state => {
          state.dragState = dragState;
        }),

      showContextMenu: (position: { x: number; y: number }) =>
        set(state => {
          state.contextMenuVisible = true;
          state.contextMenuPosition = position;
        }),

      hideContextMenu: () =>
        set(state => {
          state.contextMenuVisible = false;
          state.contextMenuPosition = null;
        }),

      // Error and status
      setGlobalError: (error: string | null) =>
        set(state => {
          state.globalError = error;
        }),

      setStatusMessage: (message: string | null, type?: 'info' | 'success' | 'warning' | 'error') =>
        set(state => {
          state.statusMessage = message;
          state.statusType = type || null;
        }),

      clearStatus: () =>
        set(state => {
          state.statusMessage = null;
          state.statusType = null;
        }),

      // Utility actions
      reset: () => set(() => ({ ...initialState })),

      getPanel: (panelId: string): any => {
        const state: any = useUIStore.getState();
        return state.panels.find((p: PanelLayoutState) => p.id === panelId);
      },

      getModal: (modalId: string) => {
        const state = useUIStore.getState();
        return state.modals.find((m: ModalState) => m.id === modalId);
      },

      getToast: (toastId: string) => {
        const state = useUIStore.getState();
        return state.toasts.find((t: ToastState) => t.id === toastId);
      },
    }))
  )
);

// Selectors for computed values
export const useUISelectors = () => {
  const store = useUIStore();

  return {
    // Layout selectors
    visiblePanels: () => {
      return store.panels.filter((panel: PanelLayoutState) => panel.isVisible);
    },

    activeSidebarPanel: () => {
      return store.sidebar.activePanel;
    },

    // Modal selectors
    activeModal: () => {
      return store.modals.find((modal: ModalState) => modal.id === store.activeModalId) || null;
    },

    topModal: () => {
      return store.modals.length > 0 ? store.modals[store.modals.length - 1] : null;
    },

    hasOpenModals: () => {
      return store.modals.length > 0;
    },

    // Toast selectors
    visibleToasts: () => {
      return store.toasts.filter((toast: ToastState) => !toast.isHidden);
    },

    toastsByType: (type: ToastType) => {
      return store.toasts.filter((toast: ToastState) => toast.type === type);
    },

    // Notification selectors
    unreadNotifications: () => {
      return store.notifications.filter((notification: NotificationState) => !notification.isRead);
    },

    notificationsByPriority: (priority: NotificationPriority) => {
      return store.notifications.filter(
        (notification: NotificationState) => notification.priority === priority
      );
    },

    // Shortcut selectors
    shortcutsForContext: (context: ShortcutContext) => {
      return store.shortcuts.filter(
        (shortcut: KeyboardShortcut) =>
          shortcut.context === context || shortcut.context === ShortcutContext.GLOBAL
      );
    },

    // Theme selectors
    effectiveColorScheme: () => {
      if (store.colorScheme === ColorScheme.AUTO) {
        // Would check system preference
        return window.matchMedia('(prefers-color-scheme: dark)').matches
          ? ColorScheme.DARK
          : ColorScheme.LIGHT;
      }
      return store.colorScheme;
    },

    // Status selectors
    hasGlobalError: () => {
      return !!store.globalError;
    },

    hasStatus: () => {
      return !!store.statusMessage;
    },

    isLoading: () => {
      return store.globalLoading;
    },

    progressPercentage: () => {
      return store.progressMax > 0 ? (store.progressValue / store.progressMax) * 100 : 0;
    },

    // Interaction selectors
    isDragging: () => {
      return !!store.dragState?.isDragging;
    },

    dragType: () => {
      return store.dragState?.type || null;
    },

    // Window state selectors
    windowState: () => {
      return {
        isMaximized: store.isMaximized,
        isMinimized: store.isMinimized,
        isFullscreen: store.isFullscreen,
        bounds: store.windowBounds,
      };
    },

    // Panel layout summary
    layoutSummary: () => {
      const visiblePanels = store.panels.filter((p: PanelLayoutState) => p.isVisible);
      const totalPanels = store.panels.length;

      return {
        totalPanels,
        visiblePanels: visiblePanels.length,
        hiddenPanels: totalPanels - visiblePanels.length,
        sidebarVisible: store.sidebar.isVisible,
        sidebarCollapsed: store.sidebar.isCollapsed,
        sidebarWidth: store.sidebar.width,
      };
    },
  };
};
