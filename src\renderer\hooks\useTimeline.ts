import { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { ElectronAPI } from '../../preload/index';
import {
  TimelineEntry,
  TimelineQuery,
  TimelineFilter,
  CheckpointId,
  ApplicationState,
  DiffResult,
  VisualDiff,
  Branch,
  BranchId,
  UndoRedoState,
  MergeResult,
  DocumentVersion,
  TimelineSortField,
  SortDirection,
} from '../../shared/types/Timeline';

// Helper to get properly typed electronAPI
const getElectronAPI = (): ElectronAPI => {
  return (window as any).electronAPI as ElectronAPI;
};

export interface UseTimelineOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableRealtime?: boolean;
}

export interface UseTimelineResult {
  // Data
  entries: TimelineEntry[];
  branches: Branch[];
  undoRedoState: UndoRedoState;
  currentBranch: Branch | null;

  // Loading states
  isLoading: boolean;
  isCreatingCheckpoint: boolean;
  isUndoing: boolean;
  isRedoing: boolean;
  isSwitchingBranch: boolean;

  // Error states
  error: Error | null;

  // Actions
  createCheckpoint: (description?: string) => Promise<CheckpointId>;
  restoreCheckpoint: (checkpointId: CheckpointId) => Promise<ApplicationState>;
  undo: () => Promise<ApplicationState | null>;
  redo: () => Promise<ApplicationState | null>;

  // Branch operations
  createBranch: (
    fromCheckpoint: CheckpointId,
    name: string,
    description?: string
  ) => Promise<BranchId>;
  switchBranch: (branchId: BranchId) => Promise<void>;
  mergeBranches: (sourceBranch: BranchId, targetBranch: BranchId) => Promise<MergeResult>;

  // Query operations
  getTimeline: (query?: TimelineQuery) => Promise<TimelineEntry[]>;
  createDiff: (before: ApplicationState, after: ApplicationState) => Promise<DiffResult>;
  createVisualDiff: (before: any, after: any) => Promise<VisualDiff>;
  getDocumentHistory: (documentId: string) => Promise<DocumentVersion[]>;

  // Utility functions
  refetch: () => void;
  invalidateCache: () => void;
}

export const useTimeline = (options: UseTimelineOptions = {}): UseTimelineResult => {
  const {
    autoRefresh = true,
    refreshInterval = 30000, // 30 seconds
    enableRealtime = false,
  } = options;

  const queryClient = useQueryClient();
  const [filter] = useState<TimelineFilter>({});
  const [sort] = useState<{ field: TimelineSortField; direction: SortDirection }>({
    field: TimelineSortField.CREATED_AT,
    direction: SortDirection.DESC,
  });

  // Query for timeline entries
  const {
    data: entries = [],
    isLoading: isLoadingEntries,
    error: entriesError,
    refetch: refetchEntries,
  } = useQuery({
    queryKey: ['timeline', 'entries', filter, sort],
    queryFn: async () => {
      const query: TimelineQuery = {
        filter,
        sort,
        pagination: { offset: 0, limit: 1000 },
      };

      // This would call the IPC service to get timeline entries
      const result = await getElectronAPI().timeline?.getTimeline(query);
      return result || [];
    },
    refetchInterval: autoRefresh ? refreshInterval : false,
    staleTime: 10000, // 10 seconds
  });

  // Query for branches
  const {
    data: branches = [],
    isLoading: isLoadingBranches,
    error: branchesError,
    refetch: refetchBranches,
  } = useQuery({
    queryKey: ['timeline', 'branches'],
    queryFn: async () => {
      const result = await getElectronAPI().timeline?.getBranches();
      return result || [];
    },
    refetchInterval: autoRefresh ? refreshInterval : false,
    staleTime: 30000, // 30 seconds
  });

  // Query for undo/redo state
  const {
    data: undoRedoState = {
      undoStack: [],
      redoStack: [],
      currentPosition: 0,
      maxStackSize: 100,
      canUndo: false,
      canRedo: false,
    },
    isLoading: isLoadingUndoRedo,
    error: undoRedoError,
    refetch: refetchUndoRedo,
  } = useQuery({
    queryKey: ['timeline', 'undoRedo'],
    queryFn: async () => {
      const result = await getElectronAPI().timeline?.getUndoRedoState();
      return (
        result || {
          undoStack: [],
          redoStack: [],
          currentPosition: 0,
          maxStackSize: 100,
          canUndo: false,
          canRedo: false,
        }
      );
    },
    refetchInterval: autoRefresh ? refreshInterval / 2 : false,
    staleTime: 5000, // 5 seconds
  });

  // Mutations
  const createCheckpointMutation = useMutation({
    mutationFn: async (description?: string) => {
      const currentState = await getElectronAPI().app?.getCurrentState();
      if (!currentState) {
        throw new Error('Failed to get current application state');
      }
      const result = await getElectronAPI().timeline?.createCheckpoint(
        currentState,
        description || ''
      );
      if (!result) {
        throw new Error('Failed to create checkpoint');
      }
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timeline'] });
    },
  });

  const restoreCheckpointMutation = useMutation({
    mutationFn: async (checkpointId: CheckpointId) => {
      return await getElectronAPI().timeline?.restoreCheckpoint(checkpointId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timeline'] });
      queryClient.invalidateQueries({ queryKey: ['app', 'state'] });
    },
  });

  const undoMutation = useMutation({
    mutationFn: async () => {
      return await getElectronAPI().timeline?.undo();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timeline'] });
      queryClient.invalidateQueries({ queryKey: ['app', 'state'] });
    },
  });

  const redoMutation = useMutation({
    mutationFn: async () => {
      return await getElectronAPI().timeline?.redo();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timeline'] });
      queryClient.invalidateQueries({ queryKey: ['app', 'state'] });
    },
  });

  const createBranchMutation = useMutation({
    mutationFn: async ({
      fromCheckpoint,
      name,
      description,
    }: {
      fromCheckpoint: CheckpointId;
      name: string;
      description?: string;
    }) => {
      return await getElectronAPI().timeline?.createBranch(fromCheckpoint, name, description);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timeline', 'branches'] });
    },
  });

  const switchBranchMutation = useMutation({
    mutationFn: async (branchId: BranchId) => {
      return await getElectronAPI().timeline?.switchBranch(branchId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timeline'] });
      queryClient.invalidateQueries({ queryKey: ['app', 'state'] });
    },
  });

  const mergeBranchesMutation = useMutation({
    mutationFn: async ({
      sourceBranch,
      targetBranch,
    }: {
      sourceBranch: BranchId;
      targetBranch: BranchId;
    }) => {
      return await getElectronAPI().timeline?.mergeBranches(sourceBranch, targetBranch);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['timeline'] });
    },
  });

  // Computed values
  const currentBranch = useMemo(() => {
    return branches.find((branch: Branch) => branch.isActive) || null;
  }, [branches]);

  const isLoading = isLoadingEntries || isLoadingBranches || isLoadingUndoRedo;
  const error = entriesError || branchesError || undoRedoError;

  // Action functions
  const createCheckpoint = useCallback(
    async (description?: string): Promise<CheckpointId> => {
      const result = await createCheckpointMutation.mutateAsync(description || '');
      return result;
    },
    [createCheckpointMutation]
  );

  const restoreCheckpoint = useCallback(
    async (checkpointId: CheckpointId): Promise<ApplicationState> => {
      const result = await restoreCheckpointMutation.mutateAsync(checkpointId);
      return result;
    },
    [restoreCheckpointMutation]
  );

  const undo = useCallback(async (): Promise<ApplicationState | null> => {
    const result = await undoMutation.mutateAsync();
    return result;
  }, [undoMutation]);

  const redo = useCallback(async (): Promise<ApplicationState | null> => {
    const result = await redoMutation.mutateAsync();
    return result;
  }, [redoMutation]);

  const createBranch = useCallback(
    async (fromCheckpoint: CheckpointId, name: string, description?: string): Promise<BranchId> => {
      const result = await createBranchMutation.mutateAsync({
        fromCheckpoint,
        name,
        ...(description && { description }),
      });
      return result;
    },
    [createBranchMutation]
  );

  const switchBranch = useCallback(
    async (branchId: BranchId): Promise<void> => {
      await switchBranchMutation.mutateAsync(branchId);
    },
    [switchBranchMutation]
  );

  const mergeBranches = useCallback(
    async (sourceBranch: BranchId, targetBranch: BranchId): Promise<MergeResult> => {
      const result = await mergeBranchesMutation.mutateAsync({ sourceBranch, targetBranch });
      return result;
    },
    [mergeBranchesMutation]
  );

  // Query functions
  const getTimeline = useCallback(async (query?: TimelineQuery): Promise<TimelineEntry[]> => {
    const result = await getElectronAPI().timeline?.getTimeline(query);
    return result || [];
  }, []);

  const createDiff = useCallback(
    async (before: ApplicationState, after: ApplicationState): Promise<DiffResult> => {
      const result = await getElectronAPI().timeline?.createDiff(before, after);
      return result;
    },
    []
  );

  const createVisualDiff = useCallback(async (before: any, after: any): Promise<VisualDiff> => {
    const result = await getElectronAPI().timeline?.createVisualDiff(before, after);
    return result;
  }, []);

  const getDocumentHistory = useCallback(async (documentId: string): Promise<DocumentVersion[]> => {
    const result = await getElectronAPI().timeline?.getDocumentHistory(documentId);
    return result || [];
  }, []);

  // Utility functions
  const refetch = useCallback(() => {
    refetchEntries();
    refetchBranches();
    refetchUndoRedo();
  }, [refetchEntries, refetchBranches, refetchUndoRedo]);

  const invalidateCache = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['timeline'] });
  }, [queryClient]);

  // Real-time updates (if enabled)
  useEffect(() => {
    if (!enableRealtime) return;

    const handleTimelineUpdate = () => {
      refetch();
    };

    // Listen for timeline updates from the main process
    getElectronAPI().timeline?.onTimelineUpdate?.(handleTimelineUpdate);

    return () => {
      getElectronAPI().timeline?.offTimelineUpdate?.(handleTimelineUpdate);
    };
  }, [enableRealtime, refetch]);

  return {
    // Data
    entries,
    branches,
    undoRedoState,
    currentBranch,

    // Loading states
    isLoading,
    isCreatingCheckpoint: createCheckpointMutation.isPending,
    isUndoing: undoMutation.isPending,
    isRedoing: redoMutation.isPending,
    isSwitchingBranch: switchBranchMutation.isPending,

    // Error states
    error,

    // Actions
    createCheckpoint,
    restoreCheckpoint,
    undo,
    redo,

    // Branch operations
    createBranch,
    switchBranch,
    mergeBranches,

    // Query operations
    getTimeline,
    createDiff,
    createVisualDiff,
    getDocumentHistory,

    // Utility functions
    refetch,
    invalidateCache,
  };
};

// Additional hooks for specific timeline operations
export const useTimelineFilter = () => {
  const [filter, setFilter] = useState<TimelineFilter>({});

  const updateFilter = useCallback((newFilter: Partial<TimelineFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter }));
  }, []);

  const clearFilter = useCallback(() => {
    setFilter({});
  }, []);

  return {
    filter,
    setFilter,
    updateFilter,
    clearFilter,
  };
};

export const useTimelineSort = () => {
  const [sort, setSort] = useState<{ field: TimelineSortField; direction: SortDirection }>({
    field: TimelineSortField.CREATED_AT,
    direction: SortDirection.DESC,
  });

  const updateSort = useCallback((field: TimelineSortField, direction?: SortDirection) => {
    setSort(prev => ({
      field,
      direction:
        direction ||
        (prev.field === field && prev.direction === SortDirection.ASC
          ? SortDirection.DESC
          : SortDirection.ASC),
    }));
  }, []);

  return {
    sort,
    setSort,
    updateSort,
  };
};

export const useCheckpoints = () => {
  const { entries, createCheckpoint, restoreCheckpoint } = useTimeline();

  const checkpoints = useMemo(() => {
    return entries.filter(entry => entry.action === 'checkpoint_created');
  }, [entries]);

  return {
    checkpoints,
    createCheckpoint,
    restoreCheckpoint,
  };
};

export const useBranches = () => {
  const { branches, createBranch, switchBranch, mergeBranches, currentBranch } = useTimeline();

  const activeBranches = useMemo(() => {
    return branches.filter(branch => !branch.metadata.tags.includes('archived'));
  }, [branches]);

  const protectedBranches = useMemo(() => {
    return branches.filter(branch => branch.isProtected);
  }, [branches]);

  return {
    branches,
    activeBranches,
    protectedBranches,
    currentBranch,
    createBranch,
    switchBranch,
    mergeBranches,
  };
};

// Enhanced hook for collaborative timeline features
export const useCollaborativeTimeline = (
  options: {
    enableRealTimeSync?: boolean;
    syncInterval?: number;
    conflictResolution?: 'auto' | 'manual';
  } = {}
) => {
  const {
    enableRealTimeSync = true,
    syncInterval = 5000, // 5 seconds
  } = options;

  const queryClient = useQueryClient();
  const [syncStatus, setSyncStatus] = useState<'synced' | 'syncing' | 'conflict' | 'error'>(
    'synced'
  );
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  const [conflicts, setConflicts] = useState<any[]>([]);

  // Query for collaborative state
  const {
    data: collaborativeState,
    isLoading: isSyncLoading,
    error: syncError,
  } = useQuery({
    queryKey: ['timeline', 'collaborative'],
    queryFn: async () => {
      // This would call an IPC method to get collaborative state
      return {
        activeUsers: [],
        pendingChanges: [],
        lastSync: new Date(),
        conflicts: [],
      };
    },
    refetchInterval: enableRealTimeSync ? syncInterval : false,
    staleTime: 1000, // 1 second
  });

  // Mutation for syncing timeline changes
  const syncTimelineChanges = useMutation({
    mutationKey: ['timeline-sync'],
    mutationFn: async (changes: any[]) => {
      setSyncStatus('syncing');

      // This would call an IPC method to sync changes
      // For now, we'll simulate the sync process
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        synced: changes.length,
        conflicts: [],
        timestamp: new Date(),
      };
    },
    onSuccess: data => {
      setSyncStatus('synced');
      setLastSyncTime(data.timestamp);

      if (data.conflicts.length > 0) {
        setSyncStatus('conflict');
        setConflicts(data.conflicts);
      }

      // Invalidate timeline queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['timeline'] });
    },
    onError: () => {
      setSyncStatus('error');
    },
  });

  // Mutation for resolving conflicts
  const resolveConflicts = useMutation({
    mutationKey: ['timeline-resolve-conflicts'],
    mutationFn: async (resolutions: any[]) => {
      // This would call an IPC method to resolve conflicts
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        resolved: resolutions.length,
        timestamp: new Date(),
      };
    },
    onSuccess: () => {
      setConflicts([]);
      setSyncStatus('synced');
      queryClient.invalidateQueries({ queryKey: ['timeline'] });
    },
  });

  // Auto-sync effect
  useEffect(() => {
    if (!enableRealTimeSync) return;

    const interval = setInterval(() => {
      // Check for pending changes and sync if needed
      const pendingChanges = collaborativeState?.pendingChanges || [];
      if (pendingChanges.length > 0) {
        syncTimelineChanges.mutate(pendingChanges);
      }
    }, syncInterval);

    return () => clearInterval(interval);
  }, [enableRealTimeSync, syncInterval, collaborativeState?.pendingChanges]);

  return {
    // Sync state
    syncStatus,
    lastSyncTime,
    conflicts,
    isSyncLoading,
    syncError,

    // Collaborative data
    activeUsers: collaborativeState?.activeUsers || [],
    pendingChanges: collaborativeState?.pendingChanges || [],

    // Actions
    syncChanges: syncTimelineChanges.mutate,
    resolveConflicts: resolveConflicts.mutate,

    // Loading states
    isSyncing: syncTimelineChanges.isPending,
    isResolvingConflicts: resolveConflicts.isPending,
  };
};
