{"name": "ai-document-processor", "productName": "AI Document Processor", "version": "1.0.0", "description": "Enterprise-grade AI-powered document processing and intelligent paperwork management system", "main": "dist/main/index.js", "homepage": "https://github.com/hepzceo/ai-document-processor", "repository": {"type": "git", "url": "https://github.com/hepzceo/ai-document-processor.git"}, "scripts": {"start": "electron-forge start", "dev": "concurrently \"npm run build:css:watch\" \"npm run dev:renderer\" \"npm run dev:main\" \"npm run dev:preload\"", "dev:main": "webpack --config config/webpack.main.config.js --mode development --watch", "dev:preload": "webpack --config config/webpack.preload.config.js --mode development --watch", "dev:renderer": "webpack serve --config config/webpack.renderer.config.js --mode development", "build": "npm run build:css && npm run build:main && npm run build:preload && npm run build:renderer", "build:main": "webpack --config config/webpack.main.config.js --mode production", "build:preload": "webpack --config config/webpack.preload.config.js --mode production", "build:renderer": "webpack --config config/webpack.renderer.config.js --mode production", "build:css": "tailwindcss -i src/renderer/styles/globals.css -o public/output.css --minify", "build:css:watch": "tailwindcss -i src/renderer/styles/globals.css -o public/output.css --watch", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "dist": "electron-builder", "type-check": "concurrently \"tsc --noEmit -p tsconfig.main.json\" \"tsc --noEmit -p tsconfig.renderer.json\"", "type-check:main": "tsc --noEmit -p tsconfig.main.json ; tsc --noEmit -p tsconfig.renderer.json", "type-check:renderer": "tsc --noEmit -p tsconfig.renderer.json", "lint": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "lint:check": "eslint src --ext .ts,.tsx,.js,.jsx", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:e2e": "playwright test", "test:performance": "jest --testPathPattern=tests/performance", "test:security": "jest --testPathPattern=tests/security", "db:migrate": "knex migrate:latest", "db:rollback": "knex migrate:rollback", "db:seed": "knex seed:run", "db:reset": "knex migrate:rollback:all && knex migrate:latest && knex seed:run", "analyze": "webpack-bundle-analyzer dist/renderer/main.js", "prepare": "husky install", "clean": "rimraf dist out coverage .nyc_output temp cache logs/*.log", "clean:deps": "rimraf node_modules package-lock.json && npm install", "clean:all": "npm run clean && npm run clean:deps", "precommit": "lint-staged", "prepush": "npm run test && npm run lint:check", "postinstall": "electron-builder install-app-deps", "validate": "npm run type-check && npm run lint:check && npm run format:check && npm run test", "ci": "npm ci && npm run validate && npm run build", "dev:debug": "cross-env NODE_ENV=development DEBUG=* npm run dev", "build:analyze": "npm run build && npm run analyze", "test:ci": "jest --ci --coverage --watchAll=false --passWithNoTests", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand --no-cache", "test:update-snapshots": "jest --updateSnapshot", "security:audit": "npm audit --audit-level moderate", "security:fix": "npm audit fix", "deps:check": "npm outdated", "deps:update": "npm update", "release:patch": "npm version patch && git push --follow-tags", "release:minor": "npm version minor && git push --follow-tags", "release:major": "npm version major && git push --follow-tags", "setup": "node scripts/dev-setup.js", "validate-setup": "node scripts/validate-setup.js"}, "keywords": ["electron", "ai", "document-processing", "ocr", "pdf", "forms", "automation", "typescript", "react"], "author": {"name": "hepz<PERSON>o", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.57.0", "@anthropic-ai/vertex-sdk": "^0.12.1", "@aws-sdk/client-bedrock-runtime": "^3.859.0", "@aws-sdk/credential-providers": "^3.859.0", "@azure-rest/ai-inference": "^1.0.0-beta.6", "@cerebras/cerebras_cloud_sdk": "^1.35.0", "@chroma-core/default-embed": "^0.1.8", "@google/genai": "^1.12.0", "@langchain/community": "^0.0.25", "@mistralai/mistralai": "^1.7.5", "@monaco-editor/react": "^4.6.0", "@tailwindcss/cli": "^4.1.11", "@tanstack/react-query": "^5.17.0", "@tanstack/react-query-persist-client": "^5.84.1", "@tensorflow/tfjs-node": "^4.22.0", "assert-plus": "^1.0.0", "axios": "^1.6.7", "better-sqlite3": "^12.2.0", "canvas": "^3.1.2", "chromadb": "^3.0.10", "compromise": "^14.10.0", "crypto-js": "^4.2.0", "csv-parser": "^3.0.0", "daisyui": "^5.0.50", "date-fns": "^3.3.1", "electron-squirrel-startup": "^1.0.1", "electron-store": "^8.1.0", "exceljs": "^4.4.0", "file-type": "^19.6.0", "framer-motion": "^10.16.16", "fs-extra": "^11.2.0", "jest-environment-jsdom": "^30.0.5", "jimp": "^0.22.12", "joi": "^17.12.1", "jsqr": "^1.4.0", "jszip": "^3.10.1", "knex": "^3.1.0", "langchain": "^0.3.30", "lodash": "^4.17.21", "mammoth": "^1.6.0", "material-icons": "^1.13.14", "mathjs": "^12.3.0", "natural": "^8.1.0", "node-cache": "^5.1.2", "ollama": "^0.5.16", "openai": "^4.28.0", "p-queue": "^8.0.1", "pako": "^2.1.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.4.54", "pdfkit": "^0.17.1", "quagga": "^0.12.1", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-hotkeys-hook": "^4.4.1", "react-resizable-panels": "^0.0.55", "react-router-dom": "^6.8.0", "react-virtualized": "^9.22.5", "redis": "^4.6.13", "sharp": "^0.33.5", "socket.io": "^4.7.4", "sqlite-fts-util": "^1.1.0", "sqlite3": "^5.1.7", "tesseract.js": "^6.0.1", "uuid": "^9.0.1", "zod": "^3.25.76", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.23.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.28.0", "@babel/preset-env": "^7.23.7", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@electron-forge/cli": "^7.8.2", "@electron-forge/maker-deb": "^7.8.2", "@electron-forge/maker-rpm": "^7.8.2", "@electron-forge/maker-squirrel": "^7.8.2", "@electron-forge/maker-zip": "^7.8.2", "@electron-forge/plugin-auto-unpack-natives": "^7.8.2", "@electron-forge/plugin-fuses": "^7.8.2", "@electron-forge/publisher-github": "^7.8.2", "@electron/fuses": "^2.0.0", "@playwright/test": "^1.41.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.17", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/better-sqlite3": "^7.6.13", "@types/crypto-js": "^4.2.1", "@types/electron": "^1.6.10", "@types/fs-extra": "^11.0.4", "@types/html-webpack-plugin": "^3.2.9", "@types/jest": "^29.5.14", "@types/lodash": "^4.14.202", "@types/mathjs": "^9.4.2", "@types/natural": "^5.1.5", "@types/node": "^20.11.5", "@types/pdfkit": "^0.13.4", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-virtualized": "^9.21.29", "@types/testing-library__jest-dom": "^6.0.0", "@types/uuid": "^9.0.7", "@types/webpack": "^5.28.5", "@types/webpack-dev-server": "^4.7.2", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "assert": "^2.1.0", "autoprefixer": "^10.4.21", "babel-loader": "^9.1.3", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "buffer": "^6.0.3", "concurrently": "^8.2.2", "copy-webpack-plugin": "^12.0.2", "core-js": "^3.35.1", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.1", "css-loader": "^6.11.0", "cssnano": "^6.1.2", "electron": "37.2.5", "electron-builder": "^24.9.1", "eslint": "^8.56.0", "eslint-plugin-electron": "^7.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "html-webpack-plugin": "^5.6.3", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.2.0", "node-loader": "^2.1.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "playwright": "^1.41.1", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "prettier": "^3.2.4", "process": "^0.11.10", "react-refresh": "^0.14.2", "rimraf": "^5.0.5", "stream-browserify": "^3.0.0", "style-loader": "^3.3.4", "tailwindcss": "^3.4.17", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "util": "^0.12.5", "webpack": "^5.89.0", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": ["last 1 electron version"], "development": ["last 1 electron version"]}}