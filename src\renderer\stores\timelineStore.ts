import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import {
  TimelineEntry,
  TimelineFilter,
  TimelineSortField,
  SortDirection,
  Branch,
  UndoRedoState,
  DiffResult,
  VisualDiff,
  TimelineViewMode,
  TimelineDisplayOptions,
  CheckpointId,
  BranchId,
  ApplicationState,
  MergeResult,
  ActionType,
  TimelineEntryType,
} from '../../shared/types/Timeline';
import { v4 as uuid } from 'uuid';

export interface TimelineState {
  // Data
  entries: TimelineEntry[];
  branches: Branch[];
  undoRedoState: UndoRedoState;
  selectedEntry: TimelineEntry | null;
  selectedBranch: Branch | null;

  // UI State
  filter: TimelineFilter;
  sort: { field: TimelineSortField; direction: SortDirection };
  viewMode: TimelineViewMode;
  displayOptions: TimelineDisplayOptions;
  searchTerm: string;

  // Loading states
  isLoading: boolean;
  isCreatingCheckpoint: boolean;
  isUndoing: boolean;
  isRedoing: boolean;
  isSwitchingBranch: boolean;

  // Error states
  error: string | null;

  // Diff viewer state
  currentDiff: DiffResult | null;
  currentVisualDiff: VisualDiff | null;
  showDiffViewer: boolean;

  // Panel states
  showTimelinePanel: boolean;
  showBranchPanel: boolean;
  showDiffPanel: boolean;
  timelinePanelWidth: number;

  // Actions
  setEntries: (entries: TimelineEntry[]) => void;
  setBranches: (branches: Branch[]) => void;
  setUndoRedoState: (state: UndoRedoState) => void;
  setSelectedEntry: (entry: TimelineEntry | null) => void;
  setSelectedBranch: (branch: Branch | null) => void;

  // Filter and sort actions
  setFilter: (filter: TimelineFilter) => void;
  updateFilter: (filter: Partial<TimelineFilter>) => void;
  clearFilter: () => void;
  setSort: (field: TimelineSortField, direction: SortDirection) => void;
  setSearchTerm: (term: string) => void;

  // UI actions
  setViewMode: (mode: TimelineViewMode) => void;
  setDisplayOptions: (options: Partial<TimelineDisplayOptions>) => void;

  // Loading actions
  setLoading: (loading: boolean) => void;
  setCreatingCheckpoint: (creating: boolean) => void;
  setUndoing: (undoing: boolean) => void;
  setRedoing: (redoing: boolean) => void;
  setSwitchingBranch: (switching: boolean) => void;

  // Error actions
  setError: (error: string | null) => void;
  clearError: () => void;

  // Diff actions
  setCurrentDiff: (diff: DiffResult | null) => void;
  setCurrentVisualDiff: (diff: VisualDiff | null) => void;
  setShowDiffViewer: (show: boolean) => void;

  // Panel actions
  setShowTimelinePanel: (show: boolean) => void;
  setShowBranchPanel: (show: boolean) => void;
  setShowDiffPanel: (show: boolean) => void;
  setTimelinePanelWidth: (width: number) => void;

  // Checkpoint management
  createCheckpoint: (description?: string, state?: ApplicationState) => Promise<CheckpointId>;
  restoreCheckpoint: (checkpointId: CheckpointId) => Promise<void>;
  deleteCheckpoint: (checkpointId: CheckpointId) => void;
  undo: () => Promise<void>;
  redo: () => Promise<void>;

  // Branch management
  createBranch: (name: string, fromCheckpoint?: CheckpointId) => Promise<BranchId>;
  switchBranch: (branchId: BranchId) => Promise<void>;
  deleteBranch: (branchId: BranchId) => void;
  mergeBranches: (sourceBranch: BranchId, targetBranch: BranchId) => Promise<MergeResult>;
  updateBranch: (branchId: BranchId, updates: Partial<Branch>) => void;

  // Diff operations
  createDiff: (
    beforeCheckpoint: CheckpointId,
    afterCheckpoint: CheckpointId
  ) => Promise<DiffResult>;
  createVisualDiff: (beforeState: any, afterState: any) => Promise<VisualDiff>;

  // Utility actions
  reset: () => void;
  addEntry: (entry: TimelineEntry) => void;
  updateEntry: (id: string, updates: Partial<TimelineEntry>) => void;
  removeEntry: (id: string) => void;
}

const initialState = {
  // Data
  entries: [],
  branches: [],
  undoRedoState: {
    undoStack: [],
    redoStack: [],
    currentPosition: 0,
    maxStackSize: 100,
    canUndo: false,
    canRedo: false,
  },
  selectedEntry: null,
  selectedBranch: null,

  // UI State
  filter: {},
  sort: { field: TimelineSortField.CREATED_AT, direction: SortDirection.DESC },
  viewMode: TimelineViewMode.LIST,
  displayOptions: {
    viewMode: TimelineViewMode.LIST,
    showMetadata: true,
    showThumbnails: false,
    groupByDate: false,
    groupByType: false,
    showBranches: true,
    showConnections: true,
    compactMode: false,
    colorScheme: 'default',
    fontSize: 14,
    itemSpacing: 8,
  },
  searchTerm: '',

  // Loading states
  isLoading: false,
  isCreatingCheckpoint: false,
  isUndoing: false,
  isRedoing: false,
  isSwitchingBranch: false,

  // Error states
  error: null,

  // Diff viewer state
  currentDiff: null,
  currentVisualDiff: null,
  showDiffViewer: false,

  // Panel states
  showTimelinePanel: true,
  showBranchPanel: true,
  showDiffPanel: false,
  timelinePanelWidth: 300,
};

export const useTimelineStore = create<TimelineState>()(
  subscribeWithSelector(
    immer(set => ({
      ...initialState,

      // Data actions
      setEntries: entries =>
        set(state => {
          state.entries = entries;
        }),

      setBranches: branches =>
        set(state => {
          state.branches = branches;
          // Update selected branch if it's no longer available
          if (state.selectedBranch && !branches.find(b => b.id === state.selectedBranch!.id)) {
            state.selectedBranch = branches.find(b => b.isActive) || null;
          }
        }),

      setUndoRedoState: undoRedoState =>
        set(state => {
          state.undoRedoState = undoRedoState;
        }),

      setSelectedEntry: entry =>
        set(state => {
          state.selectedEntry = entry;
        }),

      setSelectedBranch: branch =>
        set(state => {
          state.selectedBranch = branch;
        }),

      // Filter and sort actions
      setFilter: filter =>
        set(state => {
          state.filter = filter;
        }),

      updateFilter: filterUpdate =>
        set(state => {
          state.filter = { ...state.filter, ...filterUpdate };
        }),

      clearFilter: () =>
        set(state => {
          state.filter = {};
        }),

      setSort: (field, direction) =>
        set(state => {
          state.sort = { field, direction };
        }),

      setSearchTerm: term =>
        set(state => {
          state.searchTerm = term;
        }),

      // UI actions
      setViewMode: mode =>
        set(state => {
          state.viewMode = mode;
        }),

      setDisplayOptions: options =>
        set(state => {
          state.displayOptions = { ...state.displayOptions, ...options };
        }),

      // Loading actions
      setLoading: loading =>
        set(state => {
          state.isLoading = loading;
        }),

      setCreatingCheckpoint: creating =>
        set(state => {
          state.isCreatingCheckpoint = creating;
        }),

      setUndoing: undoing =>
        set(state => {
          state.isUndoing = undoing;
        }),

      setRedoing: redoing =>
        set(state => {
          state.isRedoing = redoing;
        }),

      setSwitchingBranch: switching =>
        set(state => {
          state.isSwitchingBranch = switching;
        }),

      // Error actions
      setError: error =>
        set(state => {
          state.error = error;
        }),

      clearError: () =>
        set(state => {
          state.error = null;
        }),

      // Diff actions
      setCurrentDiff: diff =>
        set(state => {
          state.currentDiff = diff;
          if (diff) {
            state.showDiffViewer = true;
          }
        }),

      setCurrentVisualDiff: diff =>
        set(state => {
          state.currentVisualDiff = diff;
          if (diff) {
            state.showDiffViewer = true;
          }
        }),

      setShowDiffViewer: show =>
        set(state => {
          state.showDiffViewer = show;
          if (!show) {
            state.currentDiff = null;
            state.currentVisualDiff = null;
          }
        }),

      // Panel actions
      setShowTimelinePanel: show =>
        set(state => {
          state.showTimelinePanel = show;
        }),

      setShowBranchPanel: show =>
        set(state => {
          state.showBranchPanel = show;
        }),

      setShowDiffPanel: show =>
        set(state => {
          state.showDiffPanel = show;
        }),

      setTimelinePanelWidth: width =>
        set(state => {
          state.timelinePanelWidth = Math.max(200, Math.min(600, width));
        }),

      // Utility actions
      reset: () => set(() => ({ ...initialState })),

      addEntry: entry =>
        set(state => {
          state.entries.unshift(entry); // Add to beginning for chronological order
        }),

      updateEntry: (id, updates) =>
        set(state => {
          const index = state.entries.findIndex((entry: TimelineEntry) => entry.id === id);
          if (index !== -1) {
            state.entries[index] = { ...state.entries[index], ...updates };
          }
        }),

      removeEntry: id =>
        set(state => {
          state.entries = state.entries.filter((entry: TimelineEntry) => entry.id !== id);
        }),

      // Checkpoint management
      createCheckpoint: async (description?: string, state?: ApplicationState) => {
        const checkpointId = uuid();
        const now = new Date();

        set(stateSet => {
          stateSet.isCreatingCheckpoint = true;
        });

        try {
          // Create timeline entry for checkpoint
          const entry: TimelineEntry = {
            id: uuid(),
            type: TimelineEntryType.CHECKPOINT,
            action: ActionType.CHECKPOINT_CREATED,
            description: description || `Checkpoint created at ${now.toLocaleString()}`,
            affectedDocuments: state?.documents.map(d => d.documentId) || [],
            userId: 'current-user',
            sessionId: 'current-session',
            metadata: {
              confidence: 1.0,
              warningCount: 0,
              tags: ['checkpoint'],
              category: 'version-control',
              priority: 'normal' as any,
              isReversible: true,
              dependencies: [],
            },
            createdAt: now,
            processingTime: 0,
          };

          set(stateSet => {
            stateSet.entries.unshift(entry);
            stateSet.undoRedoState.undoStack.push(checkpointId);
            stateSet.undoRedoState.redoStack = []; // Clear redo stack
            stateSet.undoRedoState.canUndo = true;
            stateSet.undoRedoState.canRedo = false;
            stateSet.isCreatingCheckpoint = false;
          });

          return checkpointId;
        } catch (error) {
          set(stateSet => {
            stateSet.error = `Failed to create checkpoint: ${error}`;
            stateSet.isCreatingCheckpoint = false;
          });
          throw error;
        }
      },

      restoreCheckpoint: async (checkpointId: CheckpointId) => {
        set(state => {
          state.isUndoing = true;
        });

        try {
          // Create timeline entry for restoration
          const entry: TimelineEntry = {
            id: uuid(),
            type: TimelineEntryType.CHECKPOINT,
            action: ActionType.CHECKPOINT_RESTORED,
            description: `Restored to checkpoint ${checkpointId}`,
            affectedDocuments: [],
            userId: 'current-user',
            sessionId: 'current-session',
            metadata: {
              confidence: 1.0,
              warningCount: 0,
              tags: ['restore', 'checkpoint'],
              category: 'version-control',
              priority: 'high' as any,
              isReversible: true,
              dependencies: [checkpointId],
            },
            createdAt: new Date(),
            processingTime: 0,
          };

          set(state => {
            state.entries.unshift(entry);
            state.isUndoing = false;
          });
        } catch (error) {
          set(state => {
            state.error = `Failed to restore checkpoint: ${error}`;
            state.isUndoing = false;
          });
          throw error;
        }
      },

      deleteCheckpoint: (checkpointId: CheckpointId) =>
        set(state => {
          // Remove from undo/redo stacks
          state.undoRedoState.undoStack = state.undoRedoState.undoStack.filter(
            (id: any) => id !== checkpointId
          );
          state.undoRedoState.redoStack = state.undoRedoState.redoStack.filter(
            (id: any) => id !== checkpointId
          );

          // Update can undo/redo flags
          state.undoRedoState.canUndo = state.undoRedoState.undoStack.length > 0;
          state.undoRedoState.canRedo = state.undoRedoState.redoStack.length > 0;
        }),

      undo: async () => {
        const state = useTimelineStore.getState();
        if (!state.undoRedoState.canUndo || state.undoRedoState.undoStack.length === 0) {
          return;
        }

        const checkpointId =
          state.undoRedoState.undoStack[state.undoRedoState.undoStack.length - 1];
        if (checkpointId) {
          await state.restoreCheckpoint(checkpointId);
        }

        set(stateSet => {
          const undoCheckpoint = stateSet.undoRedoState.undoStack.pop();
          if (undoCheckpoint) {
            stateSet.undoRedoState.redoStack.push(undoCheckpoint);
          }
          stateSet.undoRedoState.canUndo = stateSet.undoRedoState.undoStack.length > 0;
          stateSet.undoRedoState.canRedo = true;
        });
      },

      redo: async () => {
        const state = useTimelineStore.getState();
        if (!state.undoRedoState.canRedo || state.undoRedoState.redoStack.length === 0) {
          return;
        }

        const checkpointId =
          state.undoRedoState.redoStack[state.undoRedoState.redoStack.length - 1];
        if (checkpointId) {
          await state.restoreCheckpoint(checkpointId);
        }

        set(stateSet => {
          const redoCheckpoint = stateSet.undoRedoState.redoStack.pop();
          if (redoCheckpoint) {
            stateSet.undoRedoState.undoStack.push(redoCheckpoint);
          }
          stateSet.undoRedoState.canRedo = stateSet.undoRedoState.redoStack.length > 0;
          stateSet.undoRedoState.canUndo = true;
        });
      },

      // Branch management
      createBranch: async (name: string, fromCheckpoint?: CheckpointId) => {
        const branchId = uuid();
        const now = new Date();

        try {
          const newBranch: Branch = {
            id: branchId,
            name,
            description: `Branch created from ${fromCheckpoint || 'current state'}`,
            parentBranch: '',
            headCheckpoint: fromCheckpoint || uuid(),
            createdAt: now,
            createdBy: 'current-user',
            isActive: false,
            isProtected: false,
            metadata: {
              tags: ['branch'],
              category: 'version-control',
              purpose: 'feature-development',
              collaborators: [],
              permissions: {
                canRead: ['current-user'],
                canWrite: ['current-user'],
                canMerge: ['current-user'],
                canDelete: ['current-user'],
              },
            },
          };

          // Create timeline entry
          const entry: TimelineEntry = {
            id: uuid(),
            type: TimelineEntryType.BRANCH_OPERATION,
            action: ActionType.BRANCH_CREATED,
            description: `Created branch "${name}"`,
            affectedDocuments: [],
            userId: 'current-user',
            sessionId: 'current-session',
            metadata: {
              confidence: 1.0,
              warningCount: 0,
              tags: ['branch', 'create'],
              category: 'version-control',
              priority: 'normal' as any,
              isReversible: true,
              dependencies: fromCheckpoint ? [fromCheckpoint] : [],
            },
            createdAt: now,
            processingTime: 0,
          };

          set(state => {
            state.branches.push(newBranch);
            state.entries.unshift(entry);
          });

          return branchId;
        } catch (error) {
          set(state => {
            state.error = `Failed to create branch: ${error}`;
          });
          throw error;
        }
      },

      switchBranch: async (branchId: BranchId) => {
        set(state => {
          state.isSwitchingBranch = true;
        });

        try {
          const branch = useTimelineStore.getState().branches.find(b => b.id === branchId);
          if (!branch) {
            throw new Error(`Branch ${branchId} not found`);
          }

          // Create timeline entry
          const entry: TimelineEntry = {
            id: uuid(),
            type: TimelineEntryType.BRANCH_OPERATION,
            action: ActionType.BRANCH_SWITCHED,
            description: `Switched to branch "${branch.name}"`,
            affectedDocuments: [],
            userId: 'current-user',
            sessionId: 'current-session',
            metadata: {
              confidence: 1.0,
              warningCount: 0,
              tags: ['branch', 'switch'],
              category: 'version-control',
              priority: 'normal' as any,
              isReversible: true,
              dependencies: [branchId],
            },
            createdAt: new Date(),
            processingTime: 0,
          };

          set(state => {
            // Deactivate all branches
            state.branches.forEach((b: any) => {
              b.isActive = false;
            });

            // Activate target branch
            const targetBranch = state.branches.find((b: any) => b.id === branchId);
            if (targetBranch) {
              targetBranch.isActive = true;
              state.selectedBranch = targetBranch;
            }

            state.entries.unshift(entry);
            state.isSwitchingBranch = false;
          });
        } catch (error) {
          set(state => {
            state.error = `Failed to switch branch: ${error}`;
            state.isSwitchingBranch = false;
          });
          throw error;
        }
      },

      deleteBranch: (branchId: BranchId) =>
        set(state => {
          const branch = state.branches.find((b: any) => b.id === branchId);
          if (branch && !branch.isProtected) {
            state.branches = state.branches.filter((b: any) => b.id !== branchId);

            if (state.selectedBranch?.id === branchId) {
              state.selectedBranch = state.branches.find((b: any) => b.isActive) || null;
            }

            // Create timeline entry
            const entry: TimelineEntry = {
              id: uuid(),
              type: TimelineEntryType.BRANCH_OPERATION,
              action: ActionType.BRANCH_CREATED, // Note: There's no BRANCH_DELETED in the enum
              description: `Deleted branch "${branch.name}"`,
              affectedDocuments: [],
              userId: 'current-user',
              sessionId: 'current-session',
              metadata: {
                confidence: 1.0,
                warningCount: 0,
                tags: ['branch', 'delete'],
                category: 'version-control',
                priority: 'normal' as any,
                isReversible: false,
                dependencies: [],
              },
              createdAt: new Date(),
              processingTime: 0,
            };

            state.entries.unshift(entry);
          }
        }),

      mergeBranches: async (sourceBranch: BranchId, targetBranch: BranchId) => {
        try {
          const state = useTimelineStore.getState();
          const source = state.branches.find(b => b.id === sourceBranch);
          const target = state.branches.find(b => b.id === targetBranch);

          if (!source || !target) {
            throw new Error('Source or target branch not found');
          }

          const mergeResult: MergeResult = {
            id: uuid(),
            sourceBranch,
            targetBranch,
            resultCheckpoint: uuid(),
            conflicts: [],
            resolutions: [],
            statistics: {
              totalConflicts: 0,
              resolvedConflicts: 0,
              unresolvedConflicts: 0,
              automaticResolutions: 0,
              manualResolutions: 0,
              processingTime: 0,
            },
            success: true,
            createdAt: new Date(),
          };

          // Create timeline entry
          const entry: TimelineEntry = {
            id: uuid(),
            type: TimelineEntryType.MERGE_OPERATION,
            action: ActionType.BRANCHES_MERGED,
            description: `Merged branch "${source.name}" into "${target.name}"`,
            affectedDocuments: [],
            userId: 'current-user',
            sessionId: 'current-session',
            metadata: {
              confidence: 1.0,
              warningCount: 0,
              tags: ['branch', 'merge'],
              category: 'version-control',
              priority: 'high' as any,
              isReversible: true,
              dependencies: [sourceBranch, targetBranch],
            },
            createdAt: new Date(),
            processingTime: 0,
          };

          set(stateSet => {
            stateSet.entries.unshift(entry);
          });

          return mergeResult;
        } catch (error) {
          set(state => {
            state.error = `Failed to merge branches: ${error}`;
          });
          throw error;
        }
      },

      updateBranch: (branchId: BranchId, updates: Partial<Branch>) =>
        set(state => {
          const branch = state.branches.find((b: any) => b.id === branchId);
          if (branch) {
            Object.assign(branch, updates);
          }
        }),

      // Diff operations
      createDiff: async (beforeCheckpoint: CheckpointId, afterCheckpoint: CheckpointId) => {
        try {
          const diffResult: DiffResult = {
            id: uuid(),
            beforeCheckpoint,
            afterCheckpoint,
            changes: [], // Would be populated by actual diff algorithm
            statistics: {
              totalChanges: 0,
              addedItems: 0,
              modifiedItems: 0,
              deletedItems: 0,
              movedItems: 0,
              renamedItems: 0,
            },
            createdAt: new Date(),
          };

          set(state => {
            state.currentDiff = diffResult;
            state.showDiffViewer = true;
          });

          return diffResult;
        } catch (error) {
          set(state => {
            state.error = `Failed to create diff: ${error}`;
          });
          throw error;
        }
      },

      createVisualDiff: async (_beforeState: any, _afterState: any) => {
        try {
          const visualDiff: VisualDiff = {
            id: uuid(),
            type: 'text' as any, // Would be determined by content type
            textDiff: {
              lines: [], // Would be populated by diff algorithm
              statistics: {
                totalLines: 0,
                addedLines: 0,
                deletedLines: 0,
                modifiedLines: 0,
                unchangedLines: 0,
              },
            },
            metadata: {
              algorithm: 'myers',
              threshold: 0.8,
              processingTime: 0,
              accuracy: 0.95,
              createdAt: new Date(),
            },
          };

          set(state => {
            state.currentVisualDiff = visualDiff;
            state.showDiffViewer = true;
          });

          return visualDiff;
        } catch (error) {
          set(state => {
            state.error = `Failed to create visual diff: ${error}`;
          });
          throw error;
        }
      },
    }))
  )
);

// Selectors for computed values
export const useTimelineSelectors = () => {
  const store = useTimelineStore();

  return {
    // Filtered and sorted entries
    filteredEntries: () => {
      let entries = [...store.entries];

      // Apply search filter
      if (store.searchTerm) {
        entries = entries.filter(
          entry =>
            entry.description.toLowerCase().includes(store.searchTerm.toLowerCase()) ||
            entry.action.toLowerCase().includes(store.searchTerm.toLowerCase())
        );
      }

      // Apply type filter
      if (store.filter.types?.length) {
        entries = entries.filter(entry => store.filter.types!.includes(entry.type));
      }

      // Apply action filter
      if (store.filter.actions?.length) {
        entries = entries.filter(entry => store.filter.actions!.includes(entry.action));
      }

      // Apply date range filter
      if (store.filter.dateRange) {
        entries = entries.filter(entry => {
          const entryDate = entry.createdAt;
          return (
            entryDate >= store.filter.dateRange!.start && entryDate <= store.filter.dateRange!.end
          );
        });
      }

      // Apply user filter
      if (store.filter.users?.length) {
        entries = entries.filter(entry => store.filter.users!.includes(entry.userId || ''));
      }

      // Apply priority filter
      if (store.filter.priority?.length) {
        entries = entries.filter(entry => store.filter.priority!.includes(entry.metadata.priority));
      }

      // Apply sorting
      entries.sort((a, b) => {
        let aValue: any;
        let bValue: any;

        switch (store.sort.field) {
          case TimelineSortField.CREATED_AT:
            aValue = a.createdAt.getTime();
            bValue = b.createdAt.getTime();
            break;
          case TimelineSortField.TYPE:
            aValue = a.type;
            bValue = b.type;
            break;
          case TimelineSortField.ACTION:
            aValue = a.action;
            bValue = b.action;
            break;
          case TimelineSortField.USER:
            aValue = a.userId || '';
            bValue = b.userId || '';
            break;
          case TimelineSortField.PRIORITY:
            aValue = a.metadata.priority;
            bValue = b.metadata.priority;
            break;
          case TimelineSortField.PROCESSING_TIME:
            aValue = a.processingTime;
            bValue = b.processingTime;
            break;
          default:
            aValue = a.createdAt.getTime();
            bValue = b.createdAt.getTime();
        }

        if (store.sort.direction === SortDirection.ASC) {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
      });

      return entries;
    },

    // Current active branch
    currentBranch: () => {
      return store.branches.find(branch => branch.isActive) || null;
    },

    // Protected branches
    protectedBranches: () => {
      return store.branches.filter(branch => branch.isProtected);
    },

    // Active branches (not archived)
    activeBranches: () => {
      return store.branches.filter(branch => !branch.metadata.tags.includes('archived'));
    },

    // Timeline statistics
    timelineStats: () => {
      const entries = store.entries;
      const totalEntries = entries.length;
      const entriesByType = entries.reduce(
        (acc, entry) => {
          acc[entry.type] = (acc[entry.type] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      const entriesByUser = entries.reduce(
        (acc, entry) => {
          const user = entry.userId || 'System';
          acc[user] = (acc[user] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      return {
        totalEntries,
        entriesByType,
        entriesByUser,
        oldestEntry: entries.length > 0 ? entries[entries.length - 1]?.createdAt : null,
        newestEntry: entries.length > 0 ? entries[0]?.createdAt : null,
      };
    },
  };
};

// Persistence middleware for timeline store
export const persistTimelineStore = () => {
  const store = useTimelineStore.getState();

  // Save to localStorage
  const persistedState = {
    filter: store.filter,
    sort: store.sort,
    viewMode: store.viewMode,
    displayOptions: store.displayOptions,
    showTimelinePanel: store.showTimelinePanel,
    showBranchPanel: store.showBranchPanel,
    showDiffPanel: store.showDiffPanel,
    timelinePanelWidth: store.timelinePanelWidth,
  };

  localStorage.setItem('timeline-store', JSON.stringify(persistedState));
};

// Load persisted state
export const loadPersistedTimelineStore = () => {
  try {
    const persistedState = localStorage.getItem('timeline-store');
    if (persistedState) {
      const parsed = JSON.parse(persistedState);
      const store = useTimelineStore.getState();

      // Apply persisted state
      store.setFilter(parsed.filter || {});
      store.setSort(
        parsed.sort?.field || TimelineSortField.CREATED_AT,
        parsed.sort?.direction || SortDirection.DESC
      );
      store.setViewMode(parsed.viewMode || TimelineViewMode.LIST);
      store.setDisplayOptions(parsed.displayOptions || {});
      store.setShowTimelinePanel(parsed.showTimelinePanel ?? true);
      store.setShowBranchPanel(parsed.showBranchPanel ?? true);
      store.setShowDiffPanel(parsed.showDiffPanel ?? false);
      store.setTimelinePanelWidth(parsed.timelinePanelWidth || 300);
    }
  } catch (error) {
    console.warn('Failed to load persisted timeline store:', error);
  }
};

// Subscribe to store changes for persistence
useTimelineStore.subscribe(
  state => ({
    filter: state.filter,
    sort: state.sort,
    viewMode: state.viewMode,
    displayOptions: state.displayOptions,
    showTimelinePanel: state.showTimelinePanel,
    showBranchPanel: state.showBranchPanel,
    showDiffPanel: state.showDiffPanel,
    timelinePanelWidth: state.timelinePanelWidth,
  }),
  persistTimelineStore,
  { equalityFn: (a, b) => JSON.stringify(a) === JSON.stringify(b) }
);
