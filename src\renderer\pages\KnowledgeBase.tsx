import React from 'react';
import { useAppNavigation } from '../router/RouterProvider';
import { AppLayout } from '../components/layout';

const KnowledgeBase: React.FC = () => {
  const { navigate } = useAppNavigation();

  return (
    <AppLayout title='Knowledge Base' showRightPanel={true}>
      <div className='h-full p-8 overflow-auto'>
        <div className='max-w-4xl mx-auto'>
          <div className='mb-6'>
            <button className='btn btn-ghost btn-sm mb-4' onClick={() => navigate('/')}>
              ← Back to Dashboard
            </button>
            <h1 className='text-3xl font-bold text-base-content'>Knowledge Base</h1>
            <p className='text-base-content/70'>Search and manage extracted knowledge</p>
          </div>

          <div className='bg-base-200 rounded-lg p-8 text-center'>
            <div className='w-16 h-16 bg-info/10 rounded-full flex items-center justify-center mx-auto mb-4'>
              <svg
                className='w-8 h-8 text-info'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253'
                />
              </svg>
            </div>
            <h2 className='text-xl font-semibold mb-2'>Knowledge Base</h2>
            <p className='text-base-content/70'>
              This page will provide semantic search and knowledge management capabilities.
            </p>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default KnowledgeBase;
