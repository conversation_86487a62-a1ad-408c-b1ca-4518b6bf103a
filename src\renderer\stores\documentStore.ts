import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { persist } from './persistence';
import {
  Document,
  DocumentType,
  ProcessingStatus,
  ValidationStatus,
  AnalysisStatus,
  ProcessingResult,
  ExtractedData,
  ProcessedDocument,
} from '../../shared/types/Document';

export interface DocumentState {
  // Core document data
  documents: Map<string, Document>;
  activeDocumentId: string | null;
  recentDocuments: string[];

  // Document metadata and status
  documentMetadata: Map<string, DocumentMetadata>;
  processingStatus: Map<string, ProcessingStatus>;
  validationStatus: Map<string, ValidationStatus>;
  analysisStatus: Map<string, AnalysisStatus>;

  // Processing results and extracted data
  processingResults: Map<string, ProcessingResult>;
  extractedData: Map<string, ExtractedData[]>;
  processedDocuments: Map<string, ProcessedDocument>;

  // UI state
  isLoading: boolean;
  isProcessing: boolean;
  isSaving: boolean;
  error: string | null;

  // Selection and editing state
  selectedDocuments: Set<string>;
  modifiedDocuments: Set<string>;
  unsavedChanges: Map<string, any>;

  // Search and filtering
  searchTerm: string;
  filterType: DocumentType | null;
  filterStatus: ProcessingStatus | null;
  sortBy: DocumentSortField;
  sortDirection: 'asc' | 'desc';

  // Actions
  setDocuments: (documents: Document[]) => void;
  addDocument: (document: Document) => void;
  updateDocument: (id: string, updates: Partial<Document>) => void;
  removeDocument: (id: string) => void;
  setActiveDocument: (id: string | null) => void;

  // Status management
  setProcessingStatus: (id: string, status: ProcessingStatus) => void;
  setValidationStatus: (id: string, status: ValidationStatus) => void;
  setAnalysisStatus: (id: string, status: AnalysisStatus) => void;

  // Processing results
  setProcessingResult: (id: string, result: ProcessingResult) => void;
  setExtractedData: (id: string, data: ExtractedData[]) => void;
  setProcessedDocument: (id: string, processed: ProcessedDocument) => void;

  // UI state management
  setLoading: (loading: boolean) => void;
  setProcessing: (processing: boolean) => void;
  setSaving: (saving: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;

  // Selection management
  selectDocument: (id: string) => void;
  deselectDocument: (id: string) => void;
  selectAllDocuments: () => void;
  clearSelection: () => void;

  // Modification tracking
  markDocumentModified: (id: string, changes: any) => void;
  markDocumentSaved: (id: string) => void;
  hasUnsavedChanges: (id: string) => boolean;

  // Search and filtering
  setSearchTerm: (term: string) => void;
  setFilterType: (type: DocumentType | null) => void;
  setFilterStatus: (status: ProcessingStatus | null) => void;
  setSorting: (field: DocumentSortField, direction: 'asc' | 'desc') => void;

  // Utility actions
  reset: () => void;
  getDocument: (id: string) => Document | undefined;
  getDocumentsByType: (type: DocumentType) => Document[];
  getRecentDocuments: () => Document[];
}

export interface DocumentMetadata {
  fileSize: number;
  lastModified: Date;
  lastAccessed: Date;
  openCount: number;
  processingTime?: number;
  confidence?: number;
  tags: string[];
  customProperties: Record<string, any>;
}

export enum DocumentSortField {
  NAME = 'name',
  TYPE = 'type',
  SIZE = 'size',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  LAST_ACCESSED = 'lastAccessed',
  PROCESSING_STATUS = 'processingStatus',
}

const initialState = {
  // Core document data
  documents: new Map<string, Document>(),
  activeDocumentId: null,
  recentDocuments: [],

  // Document metadata and status
  documentMetadata: new Map<string, DocumentMetadata>(),
  processingStatus: new Map<string, ProcessingStatus>(),
  validationStatus: new Map<string, ValidationStatus>(),
  analysisStatus: new Map<string, AnalysisStatus>(),

  // Processing results and extracted data
  processingResults: new Map<string, ProcessingResult>(),
  extractedData: new Map<string, ExtractedData[]>(),
  processedDocuments: new Map<string, ProcessedDocument>(),

  // UI state
  isLoading: false,
  isProcessing: false,
  isSaving: false,
  error: null,

  // Selection and editing state
  selectedDocuments: new Set<string>(),
  modifiedDocuments: new Set<string>(),
  unsavedChanges: new Map<string, any>(),

  // Search and filtering
  searchTerm: '',
  filterType: null,
  filterStatus: null,
  sortBy: DocumentSortField.UPDATED_AT,
  sortDirection: 'desc' as const,
};

export const useDocumentStore: any = create(
  (persist as any)(
    (subscribeWithSelector as any)(
      (immer as any)((set: any, get: any) => ({
        ...initialState,

        // Document management actions
        setDocuments: (documents: Document[]) =>
          set((state: any) => {
            state.documents.clear();
            documents.forEach(doc => {
              state.documents.set(doc.id, doc);
              // Initialize metadata if not exists
              if (!state.documentMetadata.has(doc.id)) {
                state.documentMetadata.set(doc.id, {
                  fileSize: doc.size,
                  lastModified: doc.updatedAt,
                  lastAccessed: new Date(),
                  openCount: 0,
                  tags: [],
                  customProperties: {},
                });
              }
            });
          }),

        addDocument: (document: Document) =>
          set(state => {
            state.documents.set(document.id, document);

            // Initialize metadata
            state.documentMetadata.set(document.id, {
              fileSize: document.size,
              lastModified: document.updatedAt,
              lastAccessed: new Date(),
              openCount: 0,
              tags: [],
              customProperties: {},
            });

            // Initialize status
            state.processingStatus.set(document.id, ProcessingStatus.PENDING);
            state.validationStatus.set(document.id, ValidationStatus.NOT_VALIDATED);
            state.analysisStatus.set(document.id, AnalysisStatus.NOT_ANALYZED);

            // Add to recent documents
            state.recentDocuments = [
              document.id,
              ...state.recentDocuments.filter((id: string) => id !== document.id),
            ].slice(0, 10);
          }),

        updateDocument: (id: string, updates: Partial<Document>) =>
          set(state => {
            const existing = state.documents.get(id);
            if (existing) {
              const updated = { ...existing, ...updates, updatedAt: new Date() };
              state.documents.set(id, updated);

              // Update metadata
              const metadata = state.documentMetadata.get(id);
              if (metadata) {
                metadata.lastModified = updated.updatedAt;
                state.documentMetadata.set(id, metadata);
              }
            }
          }),

        removeDocument: (id: string) =>
          set(state => {
            state.documents.delete(id);
            state.documentMetadata.delete(id);
            state.processingStatus.delete(id);
            state.validationStatus.delete(id);
            state.analysisStatus.delete(id);
            state.processingResults.delete(id);
            state.extractedData.delete(id);
            state.processedDocuments.delete(id);
            state.selectedDocuments.delete(id);
            state.modifiedDocuments.delete(id);
            state.unsavedChanges.delete(id);
            state.recentDocuments = state.recentDocuments.filter((docId: string) => docId !== id);

            if (state.activeDocumentId === id) {
              state.activeDocumentId = null;
            }
          }),

        setActiveDocument: (id: string | null) =>
          set(state => {
            state.activeDocumentId = id;

            if (id) {
              // Update access metadata
              const metadata = state.documentMetadata.get(id);
              if (metadata) {
                metadata.lastAccessed = new Date();
                metadata.openCount += 1;
                state.documentMetadata.set(id, metadata);
              }

              // Add to recent documents
              state.recentDocuments = [
                id,
                ...state.recentDocuments.filter((docId: string) => docId !== id),
              ].slice(0, 10);
            }
          }),

        // Status management actions
        setProcessingStatus: (id: string, status: ProcessingStatus) =>
          set(state => {
            state.processingStatus.set(id, status);
          }),

        setValidationStatus: (id: string, status: ValidationStatus) =>
          set(state => {
            state.validationStatus.set(id, status);
          }),

        setAnalysisStatus: (id: string, status: AnalysisStatus) =>
          set(state => {
            state.analysisStatus.set(id, status);
          }),

        // Processing results actions
        setProcessingResult: (id: string, result: ProcessingResult) =>
          set(state => {
            state.processingResults.set(id, result);

            // Update metadata with processing time and confidence
            const metadata = state.documentMetadata.get(id);
            if (metadata) {
              metadata.processingTime = result.processingTime;
              metadata.confidence = result.confidence;
              state.documentMetadata.set(id, metadata);
            }
          }),

        setExtractedData: (id: string, data: ExtractedData[]) =>
          set(state => {
            state.extractedData.set(id, data);
          }),

        setProcessedDocument: (id: string, processed: ProcessedDocument) =>
          set(state => {
            state.processedDocuments.set(id, processed);
          }),

        // UI state management actions
        setLoading: (loading: boolean) =>
          set(state => {
            state.isLoading = loading;
          }),

        setProcessing: (processing: boolean) =>
          set(state => {
            state.isProcessing = processing;
          }),

        setSaving: (saving: boolean) =>
          set(state => {
            state.isSaving = saving;
          }),

        setError: (error: string | null) =>
          set(state => {
            state.error = error;
          }),

        clearError: () =>
          set(state => {
            state.error = null;
          }),

        // Selection management actions
        selectDocument: (id: string) =>
          set(state => {
            state.selectedDocuments.add(id);
          }),

        deselectDocument: (id: string) =>
          set(state => {
            state.selectedDocuments.delete(id);
          }),

        selectAllDocuments: () =>
          set(state => {
            state.selectedDocuments.clear();
            state.documents.forEach((_: Document, id: string) => {
              state.selectedDocuments.add(id);
            });
          }),

        clearSelection: () =>
          set(state => {
            state.selectedDocuments.clear();
          }),

        // Modification tracking actions
        markDocumentModified: (id: string, changes: any) =>
          set(state => {
            state.modifiedDocuments.add(id);
            state.unsavedChanges.set(id, changes);
          }),

        markDocumentSaved: (id: string) =>
          set(state => {
            state.modifiedDocuments.delete(id);
            state.unsavedChanges.delete(id);
          }),

        hasUnsavedChanges: (id: string): boolean => {
          const state = get();
          return state.modifiedDocuments.has(id);
        },

        // Search and filtering actions
        setSearchTerm: (term: string) =>
          set(state => {
            state.searchTerm = term;
          }),

        setFilterType: (type: DocumentType | null) =>
          set(state => {
            state.filterType = type;
          }),

        setFilterStatus: (status: ProcessingStatus | null) =>
          set(state => {
            state.filterStatus = status;
          }),

        setSorting: (field: DocumentSortField, direction: 'asc' | 'desc') =>
          set(state => {
            state.sortBy = field;
            state.sortDirection = direction;
          }),

        // Utility actions
        reset: () => set(() => ({ ...initialState })),

        getDocument: (id: string): Document | undefined => {
          const state = get();
          return state.documents.get(id);
        },

        getDocumentsByType: (type: DocumentType): Document[] => {
          const state = get();
          return Array.from(state.documents.values()).filter((doc: Document) => doc.type === type);
        },

        getRecentDocuments: (): Document[] => {
          const state = get();
          return state.recentDocuments
            .map((id: string) => state.documents.get(id))
            .filter(Boolean) as Document[];
        },
      }))
    ),
    {
      name: 'document-store',
      version: 1,
      partialize: (state: any) => ({
        // Only persist essential data, not UI state
        recentDocuments: state.recentDocuments,
        documentMetadata: Object.fromEntries(state.documentMetadata),
        searchTerm: state.searchTerm,
        filterType: state.filterType,
        sortBy: state.sortBy,
        sortDirection: state.sortDirection,
      }),
      merge: (persistedState: any, currentState: any) => ({
        ...currentState,
        ...persistedState,
        // Convert back to Map
        documentMetadata: new Map(Object.entries(persistedState.documentMetadata || {})),
      }),
    }
  )
);

// Selectors for computed values
export const useDocumentSelectors = () => {
  const store = useDocumentStore();

  return {
    // Filtered and sorted documents
    filteredDocuments: () => {
      let documents = Array.from(store.documents.values());

      // Apply search filter
      if (store.searchTerm) {
        const searchLower = store.searchTerm.toLowerCase();
        documents = (documents as Document[]).filter(
          (doc: Document) =>
            doc.name.toLowerCase().includes(searchLower) ||
            doc.metadata.title?.toLowerCase().includes(searchLower) ||
            doc.metadata.author?.toLowerCase().includes(searchLower)
        );
      }

      // Apply type filter
      if (store.filterType) {
        documents = (documents as Document[]).filter(
          (doc: Document) => doc.type === store.filterType
        );
      }

      // Apply status filter
      if (store.filterStatus) {
        documents = (documents as Document[]).filter(
          (doc: Document) => store.processingStatus.get(doc.id) === store.filterStatus
        );
      }

      // Apply sorting
      (documents as Document[]).sort((a: Document, b: Document) => {
        let aValue: any;
        let bValue: any;

        switch (store.sortBy) {
          case DocumentSortField.NAME:
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          case DocumentSortField.TYPE:
            aValue = a.type;
            bValue = b.type;
            break;
          case DocumentSortField.SIZE:
            aValue = a.size;
            bValue = b.size;
            break;
          case DocumentSortField.CREATED_AT:
            aValue = a.createdAt.getTime();
            bValue = b.createdAt.getTime();
            break;
          case DocumentSortField.UPDATED_AT:
            aValue = a.updatedAt.getTime();
            bValue = b.updatedAt.getTime();
            break;
          case DocumentSortField.LAST_ACCESSED:
            const aMetadata = store.documentMetadata.get(a.id);
            const bMetadata = store.documentMetadata.get(b.id);
            aValue = aMetadata?.lastAccessed.getTime() || 0;
            bValue = bMetadata?.lastAccessed.getTime() || 0;
            break;
          case DocumentSortField.PROCESSING_STATUS:
            aValue = store.processingStatus.get(a.id) || ProcessingStatus.PENDING;
            bValue = store.processingStatus.get(b.id) || ProcessingStatus.PENDING;
            break;
          default:
            aValue = a.updatedAt.getTime();
            bValue = b.updatedAt.getTime();
        }

        if (store.sortDirection === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
      });

      return documents;
    },

    // Document statistics
    documentStats: () => {
      const documents = Array.from(store.documents.values());
      const totalDocuments = documents.length;
      const documentsByType = (documents as Document[]).reduce(
        (acc: Record<DocumentType, number>, doc: Document) => {
          acc[doc.type] = (acc[doc.type] || 0) + 1;
          return acc;
        },
        {} as Record<DocumentType, number>
      );

      const documentsByStatus = (
        Array.from(store.processingStatus.entries()) as [string, ProcessingStatus][]
      ).reduce(
        (acc: Record<ProcessingStatus, number>, [_, status]: [string, ProcessingStatus]) => {
          acc[status] = (acc[status] || 0) + 1;
          return acc;
        },
        {} as Record<ProcessingStatus, number>
      );

      const totalSize = (documents as Document[]).reduce(
        (sum: number, doc: Document) => sum + doc.size,
        0
      );
      const averageSize = totalDocuments > 0 ? (totalSize as number) / totalDocuments : 0;

      return {
        totalDocuments,
        documentsByType,
        documentsByStatus,
        totalSize,
        averageSize,
        selectedCount: store.selectedDocuments.size,
        modifiedCount: store.modifiedDocuments.size,
      };
    },

    // Active document with metadata
    activeDocumentWithMetadata: () => {
      if (!store.activeDocumentId) return null;

      const document = store.documents.get(store.activeDocumentId);
      if (!document) return null;

      const metadata = store.documentMetadata.get(store.activeDocumentId);
      const processingStatus = store.processingStatus.get(store.activeDocumentId);
      const validationStatus = store.validationStatus.get(store.activeDocumentId);
      const analysisStatus = store.analysisStatus.get(store.activeDocumentId);
      const processingResult = store.processingResults.get(store.activeDocumentId);
      const extractedData = store.extractedData.get(store.activeDocumentId);

      return {
        document,
        metadata,
        processingStatus,
        validationStatus,
        analysisStatus,
        processingResult,
        extractedData,
        hasUnsavedChanges: store.modifiedDocuments.has(store.activeDocumentId),
      };
    },

    // Documents by processing status
    documentsByProcessingStatus: (status: ProcessingStatus) => {
      return (Array.from(store.documents.values()) as Document[]).filter(
        (doc: Document) => store.processingStatus.get(doc.id) === status
      );
    },

    // Documents with errors
    documentsWithErrors: () => {
      return (Array.from(store.documents.values()) as Document[]).filter((doc: Document) => {
        const result = store.processingResults.get(doc.id);
        return result && !result.success;
      });
    },
  };
};
