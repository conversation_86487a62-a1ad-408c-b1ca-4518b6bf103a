import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '../components/providers/QueryProvider';
import { toast } from 'react-hot-toast';

// Types for knowledge search
export interface KnowledgeSearchRequest {
  query: string;
  limit?: number;
  offset?: number;
  filters?: KnowledgeFilter[];
  includeMetadata?: boolean;
  similarityThreshold?: number;
}

export interface KnowledgeFilter {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'in' | 'range';
  value: any;
}

export interface KnowledgeSearchResult {
  id: string;
  content: string;
  similarity: number;
  metadata?: {
    source?: string;
    category?: string;
    tags?: string[];
    createdAt?: Date;
    updatedAt?: Date;
    [key: string]: any;
  };
  highlights?: string[];
}

export interface KnowledgeSearchResponse {
  results: KnowledgeSearchResult[];
  totalResults: number;
  query: string;
  processingTime: number;
  searchType: 'semantic' | 'keyword' | 'hybrid';
  pagination: {
    offset: number;
    limit: number;
    hasMore: boolean;
  };
}

export interface SemanticSearchRequest {
  query: string;
  embedding?: number[];
  collections?: string[];
  limit?: number;
  similarityThreshold?: number;
  includeDistances?: boolean;
}

export interface SemanticSearchResponse {
  results: SemanticSearchResult[];
  query: string;
  queryEmbedding: number[];
  totalResults: number;
  processingTime: number;
  collections: string[];
}

export interface SemanticSearchResult {
  id: string;
  content: string;
  distance: number;
  similarity: number;
  collection: string;
  metadata?: any;
}

export interface HybridSearchRequest {
  query: string;
  semanticWeight?: number;
  keywordWeight?: number;
  limit?: number;
  filters?: KnowledgeFilter[];
}

export interface HybridSearchResponse {
  results: HybridSearchResult[];
  query: string;
  semanticResults: number;
  keywordResults: number;
  combinedResults: number;
  processingTime: number;
}

export interface HybridSearchResult {
  id: string;
  content: string;
  semanticScore: number;
  keywordScore: number;
  combinedScore: number;
  metadata?: any;
  highlights?: string[];
}

// Helper function to get electronAPI
const getElectronAPI = () => {
  if (typeof window !== 'undefined' && window.electronAPI) {
    return window.electronAPI;
  }
  throw new Error('ElectronAPI not available');
};

/**
 * Hook for knowledge search operations
 */
export const useKnowledgeSearch = () => {
  const queryClient = useQueryClient();

  // Query for basic knowledge search
  const searchKnowledge = (request: KnowledgeSearchRequest) => {
    return useQuery({
      queryKey: queryKeys.knowledgeSearch(request.query),
      queryFn: async () => {
        const electronAPI = getElectronAPI();

        // Call the knowledge search service through IPC
        const results = await (electronAPI as any).queryInformation(request.query);

        const response: KnowledgeSearchResponse = {
          results:
            results?.map((item: any, index: number) => ({
              id: item.id || `result_${index}`,
              content: item.content || item.text || '',
              similarity: item.similarity || 0.8,
              metadata: item.metadata || {},
              highlights: item.highlights || [],
            })) || [],
          totalResults: results?.length || 0,
          query: request.query,
          processingTime: 500,
          searchType: 'keyword',
          pagination: {
            offset: request.offset || 0,
            limit: request.limit || 10,
            hasMore: (results?.length || 0) >= (request.limit || 10),
          },
        };

        return response;
      },
      enabled: !!request.query && request.query.length > 0,
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  // Mutation for semantic search
  const performSemanticSearch = useMutation({
    mutationKey: ['knowledge-semantic-search'],
    mutationFn: async (request: SemanticSearchRequest) => {
      const electronAPI = getElectronAPI();
      const startTime = Date.now();

      // Generate embedding for the query if not provided
      let queryEmbedding = request.embedding;
      if (!queryEmbedding) {
        queryEmbedding = await (electronAPI as any).generateEmbeddings(request.query);
      }

      // Perform semantic search
      const results = await (electronAPI as any).queryInformation(request.query);

      const response: SemanticSearchResponse = {
        results:
          results?.map((item: any, index: number) => ({
            id: item.id || `semantic_${index}`,
            content: item.content || item.text || '',
            distance: item.distance || Math.random() * 0.5,
            similarity: item.similarity || 1 - (item.distance || Math.random() * 0.5),
            collection: item.collection || 'default',
            metadata: item.metadata || {},
          })) || [],
        query: request.query,
        queryEmbedding: queryEmbedding || [],
        totalResults: results?.length || 0,
        processingTime: Date.now() - startTime,
        collections: request.collections || ['default'],
      };

      return response;
    },
    onSuccess: data => {
      toast.success(`Semantic search completed: ${data.results.length} results found`);

      // Cache the results
      queryClient.setQueryData(queryKeys.knowledgeSearch(data.query), {
        results: data.results.map(r => ({
          id: r.id,
          content: r.content,
          similarity: r.similarity,
          metadata: r.metadata,
        })),
        totalResults: data.totalResults,
        query: data.query,
        processingTime: data.processingTime,
        searchType: 'semantic',
        pagination: {
          offset: 0,
          limit: data.results.length,
          hasMore: false,
        },
      });
    },
    onError: (error: Error) => {
      toast.error(`Semantic search failed: ${error.message}`);
    },
  });

  // Mutation for hybrid search (semantic + keyword)
  const performHybridSearch = useMutation({
    mutationKey: ['knowledge-hybrid-search'],
    mutationFn: async (request: HybridSearchRequest) => {
      const startTime = Date.now();

      // Perform both semantic and keyword searches
      const semanticResults = await performSemanticSearch.mutateAsync({
        query: request.query,
        limit: request.limit || 20,
      });

      const keywordResults = await searchKnowledge({
        query: request.query,
        limit: request.limit || 20,
        filters: request.filters || [],
      }).refetch();

      // Combine and score results
      const semanticWeight = request.semanticWeight || 0.7;
      const keywordWeight = request.keywordWeight || 0.3;

      const combinedResults: HybridSearchResult[] = [];
      const seenIds = new Set<string>();

      // Add semantic results
      for (const result of semanticResults.results) {
        if (!seenIds.has(result.id)) {
          combinedResults.push({
            id: result.id,
            content: result.content,
            semanticScore: result.similarity,
            keywordScore: 0,
            combinedScore: result.similarity * semanticWeight,
            metadata: result.metadata,
          });
          seenIds.add(result.id);
        }
      }

      // Add keyword results and update scores
      if (keywordResults.data) {
        for (const result of keywordResults.data.results) {
          const existingIndex = combinedResults.findIndex(r => r.id === result.id);

          if (existingIndex >= 0) {
            // Update existing result
            const existingResult = combinedResults[existingIndex];
            if (existingResult) {
              existingResult.keywordScore = result.similarity;
              existingResult.combinedScore =
                existingResult.semanticScore * semanticWeight + result.similarity * keywordWeight;
              existingResult.highlights = result.highlights || [];
            }
          } else {
            // Add new result
            combinedResults.push({
              id: result.id,
              content: result.content,
              semanticScore: 0,
              keywordScore: result.similarity,
              combinedScore: result.similarity * keywordWeight,
              metadata: result.metadata,
              highlights: result.highlights || [],
            });
          }
        }
      }

      // Sort by combined score
      combinedResults.sort((a, b) => b.combinedScore - a.combinedScore);

      // Limit results
      const limitedResults = combinedResults.slice(0, request.limit || 10);

      const response: HybridSearchResponse = {
        results: limitedResults,
        query: request.query,
        semanticResults: semanticResults.results.length,
        keywordResults: keywordResults.data?.results.length || 0,
        combinedResults: limitedResults.length,
        processingTime: Date.now() - startTime,
      };

      return response;
    },
    onSuccess: data => {
      toast.success(`Hybrid search completed: ${data.combinedResults} results found`);
    },
    onError: (error: Error) => {
      toast.error(`Hybrid search failed: ${error.message}`);
    },
  });

  // Mutation for search suggestions
  const getSearchSuggestions = useMutation({
    mutationKey: ['knowledge-search-suggestions'],
    mutationFn: async ({ query, limit = 5 }: { query: string; limit?: number }) => {
      // This would typically call an autocomplete/suggestion service
      // For now, we'll simulate suggestions based on the query

      const suggestions = [
        `${query} examples`,
        `${query} definition`,
        `${query} best practices`,
        `${query} tutorial`,
        `${query} troubleshooting`,
      ].slice(0, limit);

      return {
        query,
        suggestions,
        count: suggestions.length,
      };
    },
    onSuccess: () => {
      // Don't show toast for suggestions
    },
    onError: (error: Error) => {
      console.error('Search suggestions failed:', error);
    },
  });

  // Mutation for saving search history
  const saveSearchHistory = useMutation({
    mutationKey: ['knowledge-save-search-history'],
    mutationFn: async ({ query, results }: { query: string; results: number }) => {
      // This would save search history to local storage or database
      const historyItem = {
        query,
        results,
        timestamp: new Date(),
      };

      // Get existing history
      const existingHistory = JSON.parse(localStorage.getItem('knowledge_search_history') || '[]');

      // Add new item and limit to 50 entries
      const updatedHistory = [historyItem, ...existingHistory].slice(0, 50);

      // Save back to localStorage
      localStorage.setItem('knowledge_search_history', JSON.stringify(updatedHistory));

      return historyItem;
    },
    onSuccess: () => {
      // Don't show toast for history saving
    },
    onError: (error: Error) => {
      console.error('Failed to save search history:', error);
    },
  });

  return {
    // Queries
    searchKnowledge,

    // Mutations
    performSemanticSearch,
    performHybridSearch,
    getSearchSuggestions,
    saveSearchHistory,

    // Loading states
    isSearching: performSemanticSearch.isPending || performHybridSearch.isPending,
    isGettingSuggestions: getSearchSuggestions.isPending,

    // Error states
    searchError: performSemanticSearch.error || performHybridSearch.error,

    // Data
    lastSemanticResults: performSemanticSearch.data,
    lastHybridResults: performHybridSearch.data,
    lastSuggestions: getSearchSuggestions.data,
  };
};
