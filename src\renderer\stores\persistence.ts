import Store from 'electron-store';
import { StateCreator } from 'zustand';
import { v4 as uuid } from 'uuid';

// Persistence configuration
export interface PersistenceConfig {
  name: string;
  version: number;
  partialize?: (state: any) => any;
  merge?: (persistedState: any, currentState: any) => any;
  skipHydration?: boolean;
  storage?: PersistenceStorage;
  migrate?: (persistedState: any, version: number) => any;
  onRehydrateStorage?: (state: any) => void;
}

export interface PersistenceStorage {
  getItem: (name: string) => any | null;
  setItem: (name: string, value: any) => void;
  removeItem: (name: string) => void;
}

export interface PersistedState {
  state: any;
  version: number;
  timestamp: number;
  checksum?: string;
}

export interface BackupEntry {
  id: string;
  name: string;
  state: any;
  timestamp: number;
  version: number;
  size: number;
  checksum: string;
}

// Electron Store implementation
class ElectronStoreAdapter implements PersistenceStorage {
  private store: Store;

  constructor(name: string) {
    this.store = new Store({
      name: `zustand-${name}`,
      defaults: {},
      serialize: JSON.stringify,
      deserialize: JSON.parse,
    });
  }

  getItem(name: string): any | null {
    try {
      return this.store.get(name, null);
    } catch (error) {
      console.error(`Failed to get item ${name}:`, error);
      return null;
    }
  }

  setItem(name: string, value: any): void {
    try {
      this.store.set(name, value);
    } catch (error) {
      console.error(`Failed to set item ${name}:`, error);
    }
  }

  removeItem(name: string): void {
    try {
      this.store.delete(name);
    } catch (error) {
      console.error(`Failed to remove item ${name}:`, error);
    }
  }

  clear(): void {
    this.store.clear();
  }

  has(name: string): boolean {
    return this.store.has(name);
  }

  size(): number {
    return this.store.size;
  }
}

// Persistence manager
export class PersistenceManager {
  private storage: PersistenceStorage;
  private backupStorage: PersistenceStorage;
  private maxBackups: number = 10;

  constructor(name: string) {
    this.storage = new ElectronStoreAdapter(name);
    this.backupStorage = new ElectronStoreAdapter(`${name}-backups`);
  }

  // Create a backup of current state
  createBackup(name: string, state: any, version: number): string {
    const backupId = uuid();
    const timestamp = Date.now();
    const serializedState = JSON.stringify(state);
    const checksum = this.calculateChecksum(serializedState);

    const backup: BackupEntry = {
      id: backupId,
      name,
      state,
      timestamp,
      version,
      size: serializedState.length,
      checksum,
    };

    // Get existing backups
    const backups = this.getBackups();
    backups.push(backup);

    // Keep only the most recent backups
    const sortedBackups = backups
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, this.maxBackups);

    this.backupStorage.setItem('backups', sortedBackups);
    return backupId;
  }

  // Get all backups
  getBackups(): BackupEntry[] {
    return this.backupStorage.getItem('backups') || [];
  }

  // Restore from backup
  restoreBackup(backupId: string): any | null {
    const backups = this.getBackups();
    const backup = backups.find(b => b.id === backupId);

    if (!backup) {
      console.error(`Backup ${backupId} not found`);
      return null;
    }

    // Verify checksum
    const serializedState = JSON.stringify(backup.state);
    const checksum = this.calculateChecksum(serializedState);

    if (checksum !== backup.checksum) {
      console.error(`Backup ${backupId} checksum mismatch`);
      return null;
    }

    return backup.state;
  }

  // Delete backup
  deleteBackup(backupId: string): void {
    const backups = this.getBackups();
    const filteredBackups = backups.filter(b => b.id !== backupId);
    this.backupStorage.setItem('backups', filteredBackups);
  }

  // Calculate checksum for integrity verification
  private calculateChecksum(data: string): string {
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  // Persist state
  persist(name: string, state: any, version: number): void {
    try {
      // Create backup before persisting
      this.createBackup(name, state, version);

      const persistedState: PersistedState = {
        state,
        version,
        timestamp: Date.now(),
        checksum: this.calculateChecksum(JSON.stringify(state)),
      };

      this.storage.setItem(name, persistedState);
    } catch (error) {
      console.error(`Failed to persist state for ${name}:`, error);
    }
  }

  // Hydrate state
  hydrate(name: string, currentVersion: number): any | null {
    try {
      const persistedData = this.storage.getItem(name);
      if (!persistedData) return null;

      const { state, version, checksum } = persistedData as PersistedState;

      // Verify checksum
      const calculatedChecksum = this.calculateChecksum(JSON.stringify(state));
      if (checksum && checksum !== calculatedChecksum) {
        console.warn(`Checksum mismatch for ${name}, skipping hydration`);
        return null;
      }

      // Handle version migration
      if (version !== currentVersion) {
        console.log(`Migrating ${name} from version ${version} to ${currentVersion}`);
        // Migration logic would go here
        return state; // For now, return as-is
      }

      return state;
    } catch (error) {
      console.error(`Failed to hydrate state for ${name}:`, error);
      return null;
    }
  }

  // Clear persisted state
  clear(name: string): void {
    this.storage.removeItem(name);
  }

  // Clear all data including backups
  clearAll(): void {
    if (this.storage instanceof ElectronStoreAdapter) {
      this.storage.clear();
    }
    if (this.backupStorage instanceof ElectronStoreAdapter) {
      this.backupStorage.clear();
    }
  }
}

// Persistence middleware for Zustand
export const persist = <T>(config: StateCreator<T>, options: PersistenceConfig) => {
  const persistenceManager = new PersistenceManager(options.name);

  return (set: any, get: any, api: any) => {
    const originalSet = set;
    const originalGet = get;

    // Enhanced set function that triggers persistence
    const enhancedSet = (partial: any, replace?: boolean) => {
      originalSet(partial, replace);

      // Persist state after update
      const currentState = originalGet();
      const stateToPartialize = options.partialize
        ? options.partialize(currentState)
        : currentState;

      // Debounce persistence to avoid excessive writes
      if (!(enhancedSet as any).persistenceTimeout) {
        (enhancedSet as any).persistenceTimeout = setTimeout(() => {
          persistenceManager.persist(options.name, stateToPartialize, options.version);
          (enhancedSet as any).persistenceTimeout = null;
        }, 100);
      }
    };

    // Initialize store
    const store = config(enhancedSet, originalGet, api);

    // Hydrate state if not skipped
    if (!options.skipHydration) {
      const persistedState = persistenceManager.hydrate(options.name, options.version);

      if (persistedState) {
        const mergedState = options.merge
          ? options.merge(persistedState, store)
          : { ...store, ...persistedState };

        // Apply hydrated state
        originalSet(mergedState, true);

        // Call rehydration callback
        if (options.onRehydrateStorage) {
          options.onRehydrateStorage(mergedState);
        }
      }
    }

    // Add persistence utilities to store
    return {
      ...store,
      // Persistence utilities
      _persist: {
        createBackup: (name?: string) =>
          persistenceManager.createBackup(name || options.name, originalGet(), options.version),
        getBackups: () => persistenceManager.getBackups(),
        restoreBackup: (backupId: string) => {
          const backupState = persistenceManager.restoreBackup(backupId);
          if (backupState) {
            originalSet(backupState, true);
          }
        },
        deleteBackup: (backupId: string) => persistenceManager.deleteBackup(backupId),
        clearPersistence: () => persistenceManager.clear(options.name),
        clearAll: () => persistenceManager.clearAll(),
        rehydrate: () => {
          const persistedState = persistenceManager.hydrate(options.name, options.version);
          if (persistedState) {
            const mergedState = options.merge
              ? options.merge(persistedState, originalGet())
              : { ...originalGet(), ...persistedState };
            originalSet(mergedState, true);
          }
        },
      },
    };
  };
};
