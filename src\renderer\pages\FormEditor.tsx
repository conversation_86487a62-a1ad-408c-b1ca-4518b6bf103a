import React from 'react';
import { useParams } from 'react-router-dom';
import { useAppNavigation } from '../router/RouterProvider';
import { AppLayout } from '../components/layout';

const FormEditor: React.FC = () => {
  const { documentId } = useParams<{ documentId: string }>();
  const { navigate } = useAppNavigation();

  return (
    <AppLayout title={`Form Editor: ${documentId}`} showRightPanel={true}>
      <div className='h-full p-8 overflow-auto'>
        <div className='max-w-4xl mx-auto'>
          <div className='mb-6'>
            <button
              className='btn btn-ghost btn-sm mb-4'
              onClick={() => navigate(`/documents/${documentId}`)}
            >
              ← Back to Document
            </button>
            <h1 className='text-3xl font-bold text-base-content'>Form Editor</h1>
            <p className='text-base-content/70'>Editing document: {documentId}</p>
          </div>

          <div className='bg-base-200 rounded-lg p-8 text-center'>
            <div className='w-16 h-16 bg-warning/10 rounded-full flex items-center justify-center mx-auto mb-4'>
              <svg
                className='w-8 h-8 text-warning'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
                />
              </svg>
            </div>
            <h2 className='text-xl font-semibold mb-2'>Form Editor</h2>
            <p className='text-base-content/70'>
              This page will provide intelligent form filling and editing capabilities.
            </p>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default FormEditor;
