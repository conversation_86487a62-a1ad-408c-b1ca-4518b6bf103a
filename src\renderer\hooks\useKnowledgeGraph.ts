import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '../components/providers/QueryProvider';
import { toast } from 'react-hot-toast';

// Types for knowledge graph
export interface KnowledgeNode {
  id: string;
  label: string;
  type: 'entity' | 'concept' | 'document' | 'category' | 'tag';
  properties: {
    content?: string;
    category?: string;
    confidence?: number;
    frequency?: number;
    importance?: number;
    [key: string]: any;
  };
  metadata?: {
    source?: string;
    createdAt?: Date;
    updatedAt?: Date;
    [key: string]: any;
  };
}

export interface KnowledgeEdge {
  id: string;
  source: string;
  target: string;
  type: 'related_to' | 'contains' | 'mentions' | 'similar_to' | 'part_of' | 'derived_from';
  weight: number;
  confidence: number;
  properties?: {
    context?: string;
    strength?: number;
    [key: string]: any;
  };
}

export interface KnowledgeGraph {
  nodes: KnowledgeNode[];
  edges: KnowledgeEdge[];
  metadata: {
    totalNodes: number;
    totalEdges: number;
    nodeTypes: { [key: string]: number };
    edgeTypes: { [key: string]: number };
    density: number;
    clusters: number;
  };
}

export interface GraphQueryRequest {
  nodeIds?: string[];
  nodeTypes?: string[];
  edgeTypes?: string[];
  maxDepth?: number;
  minConfidence?: number;
  includeMetadata?: boolean;
  layout?: 'force' | 'hierarchical' | 'circular' | 'grid';
}

export interface GraphAnalysisRequest {
  analysisType: 'centrality' | 'clustering' | 'pathfinding' | 'similarity' | 'communities';
  parameters?: {
    sourceNode?: string;
    targetNode?: string;
    algorithm?: string;
    threshold?: number;
  };
}

export interface GraphAnalysisResult {
  analysisType: string;
  results: any;
  metadata: {
    algorithm: string;
    parameters: any;
    processingTime: number;
    confidence: number;
  };
}

export interface RelationshipQuery {
  entityId: string;
  relationshipTypes?: string[];
  maxDistance?: number;
  minStrength?: number;
}

export interface RelationshipResult {
  entity: KnowledgeNode;
  relationships: {
    node: KnowledgeNode;
    edge: KnowledgeEdge;
    distance: number;
    path: string[];
  }[];
  totalRelationships: number;
}

/**
 * Hook for knowledge graph operations
 */
export const useKnowledgeGraph = () => {
  const queryClient = useQueryClient();

  // Query for getting the knowledge graph
  const getKnowledgeGraph = (_request: GraphQueryRequest = {}) => {
    return useQuery({
      queryKey: queryKeys.knowledgeGraph,
      queryFn: async () => {
        // This would call an IPC method to get the knowledge graph
        // For now, we'll simulate the response

        const nodes: KnowledgeNode[] = [
          {
            id: 'node_1',
            label: 'Document Processing',
            type: 'concept',
            properties: {
              category: 'technology',
              confidence: 0.95,
              frequency: 45,
              importance: 0.9,
            },
            metadata: {
              source: 'knowledge_base',
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          },
          {
            id: 'node_2',
            label: 'AI Analysis',
            type: 'concept',
            properties: {
              category: 'technology',
              confidence: 0.92,
              frequency: 38,
              importance: 0.85,
            },
            metadata: {
              source: 'knowledge_base',
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          },
          {
            id: 'node_3',
            label: 'Form Filling',
            type: 'concept',
            properties: {
              category: 'automation',
              confidence: 0.88,
              frequency: 25,
              importance: 0.7,
            },
            metadata: {
              source: 'knowledge_base',
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          },
        ];

        const edges: KnowledgeEdge[] = [
          {
            id: 'edge_1',
            source: 'node_1',
            target: 'node_2',
            type: 'related_to',
            weight: 0.8,
            confidence: 0.9,
            properties: {
              context: 'Both involve document analysis',
              strength: 0.8,
            },
          },
          {
            id: 'edge_2',
            source: 'node_1',
            target: 'node_3',
            type: 'contains',
            weight: 0.6,
            confidence: 0.85,
            properties: {
              context: 'Document processing includes form filling',
              strength: 0.6,
            },
          },
        ];

        const graph: KnowledgeGraph = {
          nodes,
          edges,
          metadata: {
            totalNodes: nodes.length,
            totalEdges: edges.length,
            nodeTypes: {
              concept: 3,
              entity: 0,
              document: 0,
              category: 0,
              tag: 0,
            },
            edgeTypes: {
              related_to: 1,
              contains: 1,
              mentions: 0,
              similar_to: 0,
              part_of: 0,
              derived_from: 0,
            },
            density: edges.length / ((nodes.length * (nodes.length - 1)) / 2),
            clusters: 1,
          },
        };

        return graph;
      },
      staleTime: 15 * 60 * 1000, // 15 minutes
      refetchInterval: 30 * 60 * 1000, // Background refetch every 30 minutes
    });
  };

  // Query for getting relationships for a specific entity
  const getEntityRelationships = (
    entityId: string,
    options: Omit<RelationshipQuery, 'entityId'> = {}
  ) => {
    return useQuery({
      queryKey: ['knowledge-graph', 'relationships', entityId, options],
      queryFn: async () => {
        // This would call an IPC method to get entity relationships
        // For now, we'll simulate the response

        const entity: KnowledgeNode = {
          id: entityId,
          label: 'Sample Entity',
          type: 'entity',
          properties: {
            category: 'general',
            confidence: 0.9,
          },
        };

        const relationships = [
          {
            node: {
              id: 'related_1',
              label: 'Related Concept',
              type: 'concept' as const,
              properties: { confidence: 0.85 },
            },
            edge: {
              id: 'edge_rel_1',
              source: entityId,
              target: 'related_1',
              type: 'related_to' as const,
              weight: 0.8,
              confidence: 0.85,
            },
            distance: 1,
            path: [entityId, 'related_1'],
          },
        ];

        const result: RelationshipResult = {
          entity,
          relationships,
          totalRelationships: relationships.length,
        };

        return result;
      },
      enabled: !!entityId,
      staleTime: 10 * 60 * 1000, // 10 minutes
    });
  };

  // Mutation for building/updating the knowledge graph
  const buildKnowledgeGraph = useMutation({
    mutationKey: ['knowledge-graph-build'],
    mutationFn: async ({
      sourceData,
      options = {},
    }: {
      sourceData?: 'all' | 'recent' | 'category';
      options?: {
        includeDocuments?: boolean;
        includeEntities?: boolean;
        includeConcepts?: boolean;
        minConfidence?: number;
        maxNodes?: number;
      };
    }) => {
      // This would call an IPC method to build the knowledge graph
      // For now, we'll simulate the building process

      const startTime = Date.now();

      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 3000));

      const result = {
        nodesCreated: 150,
        edgesCreated: 320,
        processingTime: Date.now() - startTime,
        sourceData: sourceData || 'all',
        options,
      };

      return result;
    },
    onSuccess: data => {
      // Invalidate graph queries to refresh the data
      queryClient.invalidateQueries({ queryKey: queryKeys.knowledgeGraph });

      toast.success(
        `Knowledge graph built! ${data.nodesCreated} nodes, ${data.edgesCreated} edges created.`
      );
    },
    onError: (error: Error) => {
      toast.error(`Failed to build knowledge graph: ${error.message}`);
    },
  });

  // Mutation for graph analysis
  const analyzeGraph = useMutation({
    mutationKey: ['knowledge-graph-analysis'],
    mutationFn: async (request: GraphAnalysisRequest) => {
      // This would call an IPC method to analyze the graph
      // For now, we'll simulate different analysis types

      const startTime = Date.now();
      let results: any;

      switch (request.analysisType) {
        case 'centrality':
          results = {
            nodes: [
              { id: 'node_1', centrality: 0.85, rank: 1 },
              { id: 'node_2', centrality: 0.72, rank: 2 },
              { id: 'node_3', centrality: 0.58, rank: 3 },
            ],
            algorithm: 'betweenness_centrality',
          };
          break;

        case 'clustering':
          results = {
            clusters: [
              { id: 'cluster_1', nodes: ['node_1', 'node_2'], cohesion: 0.8 },
              { id: 'cluster_2', nodes: ['node_3'], cohesion: 0.6 },
            ],
            modularity: 0.75,
            algorithm: 'louvain',
          };
          break;

        case 'pathfinding':
          results = {
            path: ['node_1', 'node_2', 'node_3'],
            distance: 2,
            weight: 1.4,
            algorithm: 'dijkstra',
          };
          break;

        case 'similarity':
          results = {
            similarities: [
              { node1: 'node_1', node2: 'node_2', similarity: 0.85 },
              { node1: 'node_1', node2: 'node_3', similarity: 0.62 },
            ],
            algorithm: 'cosine_similarity',
          };
          break;

        case 'communities':
          results = {
            communities: [
              { id: 'community_1', nodes: ['node_1', 'node_2'], strength: 0.9 },
              { id: 'community_2', nodes: ['node_3'], strength: 0.7 },
            ],
            algorithm: 'leiden',
          };
          break;

        default:
          throw new Error(`Unknown analysis type: ${request.analysisType}`);
      }

      const analysisResult: GraphAnalysisResult = {
        analysisType: request.analysisType,
        results,
        metadata: {
          algorithm: results.algorithm || 'unknown',
          parameters: request.parameters || {},
          processingTime: Date.now() - startTime,
          confidence: 0.85,
        },
      };

      return analysisResult;
    },
    onSuccess: data => {
      toast.success(`Graph analysis completed: ${data.analysisType}`);
    },
    onError: (error: Error) => {
      toast.error(`Graph analysis failed: ${error.message}`);
    },
  });

  // Mutation for finding similar nodes
  const findSimilarNodes = useMutation({
    mutationKey: ['knowledge-graph-similar-nodes'],
    mutationFn: async ({
      nodeId,
      similarityThreshold = 0.7,
      maxResults = 10,
    }: {
      nodeId: string;
      similarityThreshold?: number;
      maxResults?: number;
    }) => {
      // This would call an IPC method to find similar nodes
      // For now, we'll simulate the search

      const similarNodes = [
        {
          node: {
            id: 'similar_1',
            label: 'Similar Concept 1',
            type: 'concept' as const,
            properties: { confidence: 0.9 },
          },
          similarity: 0.85,
          commonNeighbors: 3,
          sharedEdges: ['edge_1', 'edge_2'],
        },
        {
          node: {
            id: 'similar_2',
            label: 'Similar Concept 2',
            type: 'concept' as const,
            properties: { confidence: 0.8 },
          },
          similarity: 0.75,
          commonNeighbors: 2,
          sharedEdges: ['edge_3'],
        },
      ];

      return {
        sourceNodeId: nodeId,
        similarNodes: similarNodes
          .filter(n => n.similarity >= similarityThreshold)
          .slice(0, maxResults),
        threshold: similarityThreshold,
        totalFound: similarNodes.length,
      };
    },
    onSuccess: data => {
      toast.success(`Found ${data.similarNodes.length} similar nodes`);
    },
    onError: (error: Error) => {
      toast.error(`Similar node search failed: ${error.message}`);
    },
  });

  return {
    // Queries
    getKnowledgeGraph,
    getEntityRelationships,

    // Mutations
    buildKnowledgeGraph,
    analyzeGraph,
    findSimilarNodes,

    // Loading states
    isBuilding: buildKnowledgeGraph.isPending,
    isAnalyzing: analyzeGraph.isPending,
    isFindingSimilar: findSimilarNodes.isPending,

    // Error states
    graphError: buildKnowledgeGraph.error || analyzeGraph.error || findSimilarNodes.error,

    // Data
    lastBuildResult: buildKnowledgeGraph.data,
    lastAnalysisResult: analyzeGraph.data,
    lastSimilarNodes: findSimilarNodes.data,
  };
};
