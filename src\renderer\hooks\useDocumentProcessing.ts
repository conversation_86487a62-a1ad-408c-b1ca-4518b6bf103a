import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { queryKeys, mutationKeys, invalidateQueries } from '../components/providers/QueryProvider';
import { toast } from 'react-hot-toast';

// Types for document processing
export interface ProcessingOptions {
  extractText?: boolean;
  extractImages?: boolean;
  extractTables?: boolean;
  detectFormFields?: boolean;
  performOCR?: boolean;
  enhanceImages?: boolean;
  preserveFormatting?: boolean;
  language?: string;
}

export interface ProcessingResult {
  documentId: string;
  success: boolean;
  content: {
    text: string;
    pages: any[];
    formFields: any[];
    images: any[];
    tables: any[];
  };
  processingTime: number;
  confidence: number;
  warnings: string[];
}

export interface DocumentUploadData {
  file: File;
  options?: ProcessingOptions;
}

export interface DocumentProcessingProgress {
  documentId: string;
  step: string;
  progress: number;
  message: string;
  startTime: Date;
}

// Helper function to get electronAPI
const getElectronAPI = () => {
  if (typeof window !== 'undefined' && window.electronAPI) {
    return window.electronAPI;
  }
  throw new Error('ElectronAPI not available');
};

/**
 * Hook for document processing operations
 */
export const useDocumentProcessing = () => {
  const queryClient = useQueryClient();

  // Query for getting document processing status
  const getProcessingStatus = (documentId: string) => {
    return useQuery({
      queryKey: queryKeys.documentProcessing(documentId),
      queryFn: async () => {
        // This would call an IPC method to get processing status
        // For now, we'll simulate the response
        return {
          documentId,
          status: 'completed',
          progress: 100,
          result: null,
        };
      },
      enabled: !!documentId,
      refetchInterval: (query: any) => {
        // Stop refetching when processing is complete
        return query?.data?.status === 'processing' ? 1000 : false;
      },
    });
  };

  // Mutation for processing a document
  const processDocument = useMutation({
    mutationKey: mutationKeys.documentProcess,
    mutationFn: async ({
      filePath,
      options: _options = {},
    }: {
      filePath: string;
      options?: ProcessingOptions;
    }) => {
      const electronAPI = getElectronAPI();

      // Call the main process to process the document
      const result = await (electronAPI as any).processDocument(filePath);

      if (!result.success) {
        throw new Error(result.error || 'Document processing failed');
      }

      return result;
    },
    onSuccess: data => {
      // Invalidate related queries
      invalidateQueries.documents();

      // Show success message
      toast.success('Document processed successfully!');

      // Update processing status cache
      queryClient.setQueryData(queryKeys.documentProcessing(data.documentId), {
        documentId: data.documentId,
        status: 'completed',
        progress: 100,
        result: data,
      });
    },
    onError: (error: Error) => {
      toast.error(`Processing failed: ${error.message}`);
    },
  });

  // Mutation for uploading and processing a document
  const uploadAndProcess = useMutation({
    mutationKey: mutationKeys.documentUpload,
    mutationFn: async ({ file, options = {} }: DocumentUploadData) => {
      // Convert file to buffer or path for processing
      const filePath = (file as any).path || file.name;

      // Process the document
      return processDocument.mutateAsync({ filePath, options });
    },
    onSuccess: _data => {
      invalidateQueries.documents();
      toast.success('Document uploaded and processed successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Upload failed: ${error.message}`);
    },
  });

  // Mutation for batch processing multiple documents
  const batchProcess = useMutation({
    mutationKey: ['document-batch-process'],
    mutationFn: async ({ files, options = {} }: { files: File[]; options?: ProcessingOptions }) => {
      const results = [];

      for (const file of files) {
        try {
          const result = await uploadAndProcess.mutateAsync({ file, options });
          results.push({ file: file.name, success: true, result });
        } catch (error) {
          results.push({
            file: file.name,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      return results;
    },
    onSuccess: results => {
      const successCount = results.filter(r => r.success).length;
      const totalCount = results.length;

      if (successCount === totalCount) {
        toast.success(`All ${totalCount} documents processed successfully!`);
      } else {
        toast.success(`${successCount}/${totalCount} documents processed successfully`);
      }

      invalidateQueries.documents();
    },
    onError: (error: Error) => {
      toast.error(`Batch processing failed: ${error.message}`);
    },
  });

  return {
    // Queries
    getProcessingStatus,

    // Mutations
    processDocument,
    uploadAndProcess,
    batchProcess,

    // Loading states
    isProcessing: processDocument.isPending || uploadAndProcess.isPending,
    isBatchProcessing: batchProcess.isPending,

    // Error states
    processingError: processDocument.error || uploadAndProcess.error || batchProcess.error,
  };
};

/**
 * Hook for document extraction operations
 */
export const useDocumentExtraction = () => {
  const queryClient = useQueryClient();

  // Query for getting extracted data
  const getExtractedData = (documentId: string) => {
    return useQuery({
      queryKey: queryKeys.documentExtraction(documentId),
      queryFn: async () => {
        // This would call an IPC method to get extracted data
        // For now, we'll simulate the response
        return {
          documentId,
          extractedData: [],
          confidence: 0.95,
          extractionTime: Date.now(),
        };
      },
      enabled: !!documentId,
      staleTime: 10 * 60 * 1000, // 10 minutes
    });
  };

  // Mutation for extracting text from document
  const extractText = useMutation({
    mutationKey: ['document-extract-text'],
    mutationFn: async ({
      documentId,
      options: _options = {},
    }: {
      documentId: string;
      options?: ProcessingOptions;
    }) => {
      const electronAPI = getElectronAPI();

      // This would call a specific text extraction method
      // For now, we'll use the general document processing
      const result = await (electronAPI as any).processDocument(documentId);

      return {
        documentId,
        text: result.content?.text || '',
        confidence: result.confidence || 0,
        extractionTime: result.processingTime || 0,
      };
    },
    onSuccess: data => {
      // Update extraction cache
      queryClient.setQueryData(queryKeys.documentExtraction(data.documentId), data);

      toast.success('Text extracted successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Text extraction failed: ${error.message}`);
    },
  });

  // Mutation for extracting structured data
  const extractStructuredData = useMutation({
    mutationKey: ['document-extract-structured'],
    mutationFn: async ({
      documentId,
      options: _options = {},
    }: {
      documentId: string;
      options?: ProcessingOptions;
    }) => {
      const electronAPI = getElectronAPI();

      // This would call a specific structured data extraction method
      const result = await (electronAPI as any).processDocument(documentId);

      return {
        documentId,
        structuredData: result.content || {},
        confidence: result.confidence || 0,
        extractionTime: result.processingTime || 0,
      };
    },
    onSuccess: data => {
      queryClient.setQueryData(queryKeys.documentExtraction(data.documentId), data);

      toast.success('Structured data extracted successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Structured data extraction failed: ${error.message}`);
    },
  });

  return {
    // Queries
    getExtractedData,

    // Mutations
    extractText,
    extractStructuredData,

    // Loading states
    isExtracting: extractText.isPending || extractStructuredData.isPending,

    // Error states
    extractionError: extractText.error || extractStructuredData.error,
  };
};
