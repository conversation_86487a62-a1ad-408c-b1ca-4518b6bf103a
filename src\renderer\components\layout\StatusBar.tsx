import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface StatusMessage {
  id: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: Date;
  duration?: number;
}

interface ProgressInfo {
  id: string;
  label: string;
  progress: number;
  total?: number;
  eta?: string;
  status: 'active' | 'completed' | 'error' | 'paused';
}

interface StatusBarProps {
  className?: string;
  showProgress?: boolean;
  showQuickActions?: boolean;
  customActions?: React.ReactNode;
}

interface SystemInfo {
  memoryUsage?: string;
  cpuUsage?: string;
  diskSpace?: string;
  networkStatus?: 'online' | 'offline';
}

export const StatusBar: React.FC<StatusBarProps> = ({
  className = '',
  showProgress = true,
  showQuickActions = true,
  customActions,
}) => {
  const [statusMessages, setStatusMessages] = useState<StatusMessage[]>([]);
  const [progressItems] = useState<ProgressInfo[]>([]);
  const [systemInfo] = useState<SystemInfo>({}); // Remove setSystemInfo since it's not used
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Add status message
  const addStatusMessage = useCallback((message: Omit<StatusMessage, 'id' | 'timestamp'>) => {
    const newMessage: StatusMessage = {
      ...message,
      id: `status-${Date.now()}-${Math.random()}`,
      timestamp: new Date(),
    };

    setStatusMessages(prev => [...prev, newMessage]);

    // Auto-remove message after duration
    if (message.duration) {
      setTimeout(() => {
        setStatusMessages(prev => prev.filter(m => m.id !== newMessage.id));
      }, message.duration);
    }
  }, []);

  // Remove status message
  const removeStatusMessage = useCallback((id: string) => {
    setStatusMessages(prev => prev.filter(m => m.id !== id));
  }, []);

  // Add progress item (commented out since not used in this component)
  // const addProgressItem = useCallback((progress: Omit<ProgressInfo, 'id'>) => {
  //   const newProgress: ProgressInfo = {
  //     ...progress,
  //     id: `progress-${Date.now()}-${Math.random()}`,
  //   };

  //   setProgressItems(prev => [...prev, newProgress]);
  // }, []);

  // Update progress item (commented out since not used in this component)
  // const updateProgressItem = useCallback((id: string, updates: Partial<ProgressInfo>) => {
  //   setProgressItems(prev => prev.map(item => (item.id === id ? { ...item, ...updates } : item)));
  // }, []);

  // Remove progress item (commented out since not used in this component)
  // const removeProgressItem = useCallback((id: string) => {
  //   setProgressItems(prev => prev.filter(item => item.id !== id));
  // }, []);

  // Get current status message
  const currentMessage = statusMessages[statusMessages.length - 1];

  // Get active progress items
  const activeProgress = progressItems.filter(item => item.status === 'active');

  // Format time
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Get status icon
  const getStatusIcon = (type: StatusMessage['type']) => {
    switch (type) {
      case 'success':
        return 'check_circle';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'info';
    }
  };

  // Get status color
  const getStatusColor = (type: StatusMessage['type']) => {
    switch (type) {
      case 'success':
        return 'text-success';
      case 'warning':
        return 'text-warning';
      case 'error':
        return 'text-error';
      default:
        return 'text-info';
    }
  };

  // Quick actions
  const quickActions = [
    {
      id: 'new-document',
      icon: 'add',
      label: 'New Document',
      onClick: () => addStatusMessage({ message: 'Creating new document...', type: 'info' }),
    },
    {
      id: 'settings',
      icon: 'settings',
      label: 'Settings',
      onClick: () => addStatusMessage({ message: 'Opening settings...', type: 'info' }),
    },
    {
      id: 'help',
      icon: 'help',
      label: 'Help',
      onClick: () => addStatusMessage({ message: 'Opening help...', type: 'info' }),
    },
  ];

  return (
    <div
      className={`h-6 bg-base-200 border-t border-base-300 flex items-center justify-between px-4 text-xs ${className}`}
    >
      {/* Left section - Status messages and progress */}
      <div className='flex items-center space-x-4 flex-1 min-w-0'>
        {/* Current status message */}
        <AnimatePresence mode='wait'>
          {currentMessage ? (
            <motion.div
              key={currentMessage.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.2 }}
              className='flex items-center space-x-2 min-w-0'
            >
              <span className={`material-icons text-sm ${getStatusColor(currentMessage.type)}`}>
                {getStatusIcon(currentMessage.type)}
              </span>
              <span className='text-base-content/80 truncate'>{currentMessage.message}</span>
              <button
                className='text-base-content/50 hover:text-base-content'
                onClick={() => removeStatusMessage(currentMessage.id)}
                title='Dismiss'
              >
                <span className='material-icons text-xs'>close</span>
              </button>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className='text-base-content/60'
            >
              Ready
            </motion.div>
          )}
        </AnimatePresence>

        {/* Active progress indicators */}
        {showProgress && activeProgress.length > 0 && (
          <div className='flex items-center space-x-3'>
            {activeProgress.slice(0, 2).map(progress => (
              <motion.div
                key={progress.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className='flex items-center space-x-2'
              >
                <div className='w-16 h-1 bg-base-300 rounded-full overflow-hidden'>
                  <motion.div
                    className='h-full bg-primary'
                    initial={{ width: 0 }}
                    animate={{ width: `${progress.progress}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
                <span className='text-base-content/70 text-xs'>
                  {progress.label} {progress.progress}%
                </span>
                {progress.eta && (
                  <span className='text-base-content/50 text-xs'>ETA: {progress.eta}</span>
                )}
              </motion.div>
            ))}
            {activeProgress.length > 2 && (
              <span className='text-base-content/50 text-xs'>
                +{activeProgress.length - 2} more
              </span>
            )}
          </div>
        )}
      </div>

      {/* Center section - System info */}
      <div className='flex items-center space-x-4 text-base-content/50'>
        {systemInfo.networkStatus && (
          <div className='flex items-center space-x-1'>
            <span
              className={`w-2 h-2 rounded-full ${
                systemInfo.networkStatus === 'online' ? 'bg-success' : 'bg-error'
              }`}
            />
            <span>{systemInfo.networkStatus}</span>
          </div>
        )}

        {systemInfo.memoryUsage && <span title='Memory Usage'>RAM: {systemInfo.memoryUsage}</span>}

        {systemInfo.cpuUsage && <span title='CPU Usage'>CPU: {systemInfo.cpuUsage}</span>}
      </div>

      {/* Right section - Quick actions and time */}
      <div className='flex items-center space-x-3'>
        {/* Quick actions */}
        {showQuickActions && (
          <div className='flex items-center space-x-1'>
            {quickActions.map(action => (
              <button
                key={action.id}
                className='p-1 rounded hover:bg-base-300 transition-colors'
                onClick={action.onClick}
                title={action.label}
              >
                <span className='material-icons text-sm text-base-content/70'>{action.icon}</span>
              </button>
            ))}
          </div>
        )}

        {/* Custom actions */}
        {customActions}

        {/* Current time */}
        <div className='text-base-content/70 font-mono'>{formatTime(currentTime)}</div>
      </div>
    </div>
  );
};

// Hook for using status bar functionality
export const useStatusBar = () => {
  const [statusBarRef, setStatusBarRef] = useState<any>(null);

  const showStatus = useCallback(
    (message: string, type: StatusMessage['type'] = 'info', duration = 3000) => {
      if (statusBarRef?.addStatusMessage) {
        statusBarRef.addStatusMessage({ message, type, duration });
      }
    },
    [statusBarRef]
  );

  const showProgress = useCallback(
    (label: string, progress: number, total?: number, eta?: string) => {
      if (statusBarRef?.addProgressItem) {
        return statusBarRef.addProgressItem({ label, progress, total, eta, status: 'active' });
      }
    },
    [statusBarRef]
  );

  const updateProgress = useCallback(
    (id: string, progress: number, eta?: string) => {
      if (statusBarRef?.updateProgressItem) {
        statusBarRef.updateProgressItem(id, { progress, eta });
      }
    },
    [statusBarRef]
  );

  const hideProgress = useCallback(
    (id: string) => {
      if (statusBarRef?.removeProgressItem) {
        statusBarRef.removeProgressItem(id);
      }
    },
    [statusBarRef]
  );

  return {
    setStatusBarRef,
    showStatus,
    showProgress,
    updateProgress,
    hideProgress,
  };
};

export default StatusBar;
