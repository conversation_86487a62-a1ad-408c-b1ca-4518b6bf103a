import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { motion, AnimatePresence } from 'framer-motion';
import { useHotkeys } from 'react-hotkeys-hook';

interface Tab {
  id: string;
  title: string;
  content: React.ReactNode;
  isDirty?: boolean;
  isClosable?: boolean;
  icon?: string;
  path?: string;
}

interface MainContentProps {
  className?: string;
  children?: React.ReactNode;
  showTabBar?: boolean;
  showRightPanel?: boolean;
  rightPanelContent?: React.ReactNode;
  rightPanelDefaultSize?: number;
  rightPanelMinSize?: number;
  tabs?: Tab[];
  activeTabId?: string;
  onTabChange?: (tabId: string) => void;
  onTabClose?: (tabId: string) => void;
  onNewTab?: () => void;
}

interface LayoutState {
  rightPanelSize: number;
  rightPanelCollapsed: boolean;
  splitView: boolean;
  splitOrientation: 'horizontal' | 'vertical';
}

const STORAGE_KEY = 'main-content-layout';
const DEFAULT_RIGHT_PANEL_SIZE = 25;
const MIN_RIGHT_PANEL_SIZE = 15;

export const MainContent: React.FC<MainContentProps> = ({
  className = '',
  children,
  showTabBar = true,
  showRightPanel = false,
  rightPanelContent,
  rightPanelDefaultSize = DEFAULT_RIGHT_PANEL_SIZE,
  rightPanelMinSize = MIN_RIGHT_PANEL_SIZE,
  tabs = [],
  activeTabId,
  onTabChange,
  onTabClose,
  onNewTab,
}) => {
  const [layoutState, setLayoutState] = useState<LayoutState>({
    rightPanelSize: rightPanelDefaultSize,
    rightPanelCollapsed: false,
    splitView: false,
    splitOrientation: 'horizontal',
  });

  const contentRef = useRef<HTMLDivElement>(null);
  const activeTab = tabs.find(tab => tab.id === activeTabId) || tabs[0];

  // Load layout state from localStorage
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        const parsed = JSON.parse(savedState);
        setLayoutState(prev => ({
          ...prev,
          ...parsed,
          rightPanelSize: Math.max(
            rightPanelMinSize,
            Math.min(50, parsed.rightPanelSize || rightPanelDefaultSize)
          ),
        }));
      }
    } catch (error) {
      console.warn('Failed to load main content layout state:', error);
    }
  }, [rightPanelDefaultSize, rightPanelMinSize]);

  // Save layout state to localStorage
  const saveLayoutState = useCallback((newState: Partial<LayoutState>) => {
    const updatedState = { ...layoutState, ...newState };
    setLayoutState(updatedState);
    
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState));
    } catch (error) {
      console.warn('Failed to save main content layout state:', error);
    }
  }, [layoutState]);

  // Handle panel resize
  const handleRightPanelResize = useCallback((size: number) => {
    if (!layoutState.rightPanelCollapsed) {
      saveLayoutState({ rightPanelSize: size });
    }
  }, [layoutState.rightPanelCollapsed, saveLayoutState]);

  // Toggle right panel
  const toggleRightPanel = useCallback(() => {
    saveLayoutState({ rightPanelCollapsed: !layoutState.rightPanelCollapsed });
  }, [layoutState.rightPanelCollapsed, saveLayoutState]);

  // Toggle split view
  const toggleSplitView = useCallback(() => {
    saveLayoutState({ splitView: !layoutState.splitView });
  }, [layoutState.splitView, saveLayoutState]);

  // Toggle split orientation
  const toggleSplitOrientation = useCallback(() => {
    saveLayoutState({
      splitOrientation: layoutState.splitOrientation === 'horizontal' ? 'vertical' : 'horizontal',
    });
  }, [layoutState.splitOrientation, saveLayoutState]);

  // Handle tab selection
  const handleTabClick = useCallback((tabId: string) => {
    onTabChange?.(tabId);
  }, [onTabChange]);

  // Handle tab close
  const handleTabClose = useCallback((tabId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    onTabClose?.(tabId);
  }, [onTabClose]);

  // Keyboard shortcuts
  useHotkeys('ctrl+t', () => onNewTab?.(), { preventDefault: true });
  useHotkeys('ctrl+w', () => activeTab && onTabClose?.(activeTab.id), { preventDefault: true });
  useHotkeys('ctrl+tab', () => {
    const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
    const nextIndex = (currentIndex + 1) % tabs.length;
    if (tabs[nextIndex]) {
      onTabChange?.(tabs[nextIndex].id);
    }
  }, { preventDefault: true });
  useHotkeys('ctrl+shift+tab', () => {
    const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
    const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
    if (tabs[prevIndex]) {
      onTabChange?.(tabs[prevIndex].id);
    }
  }, { preventDefault: true });
  useHotkeys('ctrl+shift+p', toggleRightPanel, { preventDefault: true });
  useHotkeys('ctrl+\\', toggleSplitView, { preventDefault: true });

  // Focus management
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.focus();
    }
  }, [activeTabId]);

  const renderTabBar = () => (
    <div className="h-10 bg-base-200 border-b border-base-300 flex items-center">
      {/* Tab list */}
      <div className="flex-1 flex items-center overflow-x-auto">
        <div className="flex">
          {tabs.map(tab => (
            <motion.button
              key={tab.id}
              layout
              className={`flex items-center space-x-2 px-4 py-2 text-sm border-r border-base-300 transition-colors ${
                tab.id === activeTabId
                  ? 'bg-base-100 text-base-content'
                  : 'text-base-content/70 hover:text-base-content hover:bg-base-300'
              }`}
              onClick={() => handleTabClick(tab.id)}
              title={tab.path || tab.title}
            >
              {tab.icon && (
                <span className="material-icons text-sm">{tab.icon}</span>
              )}
              <span className="truncate max-w-32">{tab.title}</span>
              {tab.isDirty && (
                <span className="w-2 h-2 bg-warning rounded-full" title="Unsaved changes" />
              )}
              {tab.isClosable !== false && (
                <button
                  className="p-0.5 rounded hover:bg-base-300 transition-colors"
                  onClick={(e) => handleTabClose(tab.id, e)}
                  title="Close tab"
                >
                  <span className="material-icons text-xs">close</span>
                </button>
              )}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Tab actions */}
      <div className="flex items-center space-x-1 px-2">
        <button
          className="p-1 rounded hover:bg-base-300 transition-colors"
          onClick={onNewTab}
          title="New tab (Ctrl+T)"
        >
          <span className="material-icons text-sm">add</span>
        </button>
        
        <button
          className="p-1 rounded hover:bg-base-300 transition-colors"
          onClick={toggleSplitView}
          title={`${layoutState.splitView ? 'Close' : 'Open'} split view (Ctrl+\\)`}
        >
          <span className="material-icons text-sm">
            {layoutState.splitView ? 'close_fullscreen' : 'open_in_full'}
          </span>
        </button>

        {layoutState.splitView && (
          <button
            className="p-1 rounded hover:bg-base-300 transition-colors"
            onClick={toggleSplitOrientation}
            title="Toggle split orientation"
          >
            <span className="material-icons text-sm">
              {layoutState.splitOrientation === 'horizontal' ? 'vertical_split' : 'horizontal_split'}
            </span>
          </button>
        )}

        {showRightPanel && (
          <button
            className="p-1 rounded hover:bg-base-300 transition-colors"
            onClick={toggleRightPanel}
            title={`${layoutState.rightPanelCollapsed ? 'Show' : 'Hide'} right panel (Ctrl+Shift+P)`}
          >
            <span className="material-icons text-sm">
              {layoutState.rightPanelCollapsed ? 'panel_right' : 'panel_right_close'}
            </span>
          </button>
        )}
      </div>
    </div>
  );

  const renderContent = () => {
    if (layoutState.splitView && activeTab) {
      return (
        <PanelGroup direction={layoutState.splitOrientation}>
          <Panel defaultSize={50} minSize={30}>
            <div className="h-full overflow-hidden">
              {activeTab.content}
            </div>
          </Panel>
          <PanelResizeHandle className="w-1 bg-base-300 hover:bg-primary transition-colors" />
          <Panel defaultSize={50} minSize={30}>
            <div className="h-full overflow-hidden bg-base-100 flex items-center justify-center">
              <div className="text-center text-base-content/50">
                <span className="material-icons text-4xl mb-2 block">content_copy</span>
                <p>Split view content</p>
              </div>
            </div>
          </Panel>
        </PanelGroup>
      );
    }

    return (
      <div className="h-full overflow-hidden">
        <AnimatePresence mode="wait">
          {activeTab ? (
            <motion.div
              key={activeTab.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
              className="h-full"
            >
              {activeTab.content}
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="h-full flex items-center justify-center bg-base-100"
            >
              {children || (
                <div className="text-center text-base-content/50">
                  <span className="material-icons text-6xl mb-4 block">description</span>
                  <h3 className="text-lg font-medium mb-2">No document open</h3>
                  <p className="text-sm">Open a document or create a new one to get started</p>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  return (
    <div
      ref={contentRef}
      className={`flex-1 flex flex-col bg-base-100 ${className}`}
      tabIndex={-1}
      role="main"
      aria-label="Main content area"
    >
      {/* Tab bar */}
      {showTabBar && tabs.length > 0 && renderTabBar()}

      {/* Content area */}
      <div className="flex-1 flex overflow-hidden">
        {showRightPanel && !layoutState.rightPanelCollapsed ? (
          <PanelGroup direction="horizontal">
            <Panel defaultSize={100 - layoutState.rightPanelSize} minSize={50}>
              {renderContent()}
            </Panel>
            <PanelResizeHandle className="w-1 bg-base-300 hover:bg-primary transition-colors" />
            <Panel
              defaultSize={layoutState.rightPanelSize}
              minSize={rightPanelMinSize}
              maxSize={50}
              onResize={handleRightPanelResize}
            >
              <div className="h-full bg-base-200 border-l border-base-300 overflow-hidden">
                {rightPanelContent || (
                  <div className="p-4 text-center text-base-content/50">
                    <span className="material-icons text-4xl mb-2 block">info</span>
                    <p>Right panel content</p>
                  </div>
                )}
              </div>
            </Panel>
          </PanelGroup>
        ) : (
          renderContent()
        )}
      </div>
    </div>
  );
};

export default MainContent;
