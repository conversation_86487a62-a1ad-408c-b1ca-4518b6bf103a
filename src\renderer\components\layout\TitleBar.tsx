import React, { useState, useEffect } from 'react';
// import { useLocation } from 'react-router-dom';
import { useRouteMetadata } from '../../router/RouterProvider';

interface TitleBarProps {
  title?: string;
  showWindowControls?: boolean;
  className?: string;
  onMenuClick?: () => void;
}

interface WindowControls {
  minimize: () => Promise<void>;
  maximize: () => Promise<void>;
  close: () => Promise<void>;
  isMaximized: () => Promise<boolean>;
}

export const TitleBar: React.FC<TitleBarProps> = ({
  title,
  showWindowControls = true,
  className = '',
  onMenuClick,
}) => {
  const routeMetadata = useRouteMetadata();
  const [isMaximized, setIsMaximized] = useState(false);
  const [windowControls, setWindowControls] = useState<WindowControls | null>(null);

  // Get the display title
  const displayTitle = title || routeMetadata?.title || 'AI Document Processor';
  const fullTitle = `${displayTitle} - AI Document Processor`;

  useEffect(() => {
    // Initialize window controls if available
    if (window.electronAPI && 'windowControls' in window.electronAPI) {
      const electronAPI = window.electronAPI as any;
      const controls: WindowControls = {
        minimize: async () => {
          if (electronAPI?.windowControls?.minimize) {
            await electronAPI.windowControls.minimize();
          }
        },
        maximize: async () => {
          if (electronAPI?.windowControls?.maximize) {
            await electronAPI.windowControls.maximize();
          }
        },
        close: async () => {
          if (electronAPI?.windowControls?.close) {
            await electronAPI.windowControls.close();
          }
        },
        isMaximized: () => {
          return electronAPI?.windowControls?.isMaximized?.() || Promise.resolve(false);
        },
      };
      setWindowControls(controls);
      controls
        .isMaximized()
        .then(setIsMaximized)
        .catch(() => setIsMaximized(false));
    }

    // Update document title
    document.title = fullTitle;
  }, [fullTitle]);

  // Handle window state changes
  useEffect(() => {
    const handleWindowStateChange = () => {
      if (windowControls) {
        windowControls
          .isMaximized()
          .then(setIsMaximized)
          .catch(() => setIsMaximized(false));
      }
    };

    // Listen for window state changes if available
    if (window.electronAPI && 'windowControls' in window.electronAPI) {
      const electronAPI = window.electronAPI as any;
      if (electronAPI?.windowControls?.onStateChange) {
        electronAPI.windowControls.onStateChange(handleWindowStateChange);
      }
    }

    return () => {
      // Cleanup listener if available
      if (window.electronAPI && 'windowControls' in window.electronAPI) {
        const electronAPI = window.electronAPI as any;
        if (electronAPI?.windowControls?.removeStateListener) {
          electronAPI.windowControls.removeStateListener(handleWindowStateChange);
        }
      }
    };
  }, [windowControls]);

  const handleMinimize = () => {
    void windowControls?.minimize();
  };

  const handleMaximize = () => {
    void windowControls?.maximize();
    setIsMaximized(!isMaximized);
  };

  const handleClose = () => {
    void windowControls?.close();
  };

  const handleMenuClick = () => {
    onMenuClick?.();
  };

  return (
    <div
      className={`h-8 bg-base-200 border-b border-base-300 flex items-center justify-between px-4 select-none ${className}`}
      style={{ WebkitAppRegion: 'drag' } as React.CSSProperties}
    >
      {/* Left section - Menu and title */}
      <div className='flex items-center space-x-3 flex-1 min-w-0'>
        {/* Menu button */}
        <button
          className='flex items-center justify-center w-6 h-6 rounded hover:bg-base-300 transition-colors'
          onClick={handleMenuClick}
          style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
          title='Application Menu'
        >
          <svg
            className='w-4 h-4 text-base-content/70'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M4 6h16M4 12h16M4 18h16'
            />
          </svg>
        </button>

        {/* Application icon and title */}
        <div className='flex items-center space-x-2 min-w-0'>
          <div className='w-5 h-5 bg-primary rounded flex items-center justify-center flex-shrink-0'>
            <svg className='w-3 h-3 text-primary-content' fill='currentColor' viewBox='0 0 24 24'>
              <path d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' />
            </svg>
          </div>
          <span className='text-sm font-medium text-base-content truncate'>{displayTitle}</span>
        </div>
      </div>

      {/* Center section - Breadcrumb or additional info */}
      <div className='flex items-center space-x-2 text-xs text-base-content/50'>
        {routeMetadata?.breadcrumb && <span>{routeMetadata.breadcrumb}</span>}
      </div>

      {/* Right section - Window controls */}
      {showWindowControls && (
        <div
          className='flex items-center space-x-1'
          style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
        >
          {/* Minimize button */}
          <button
            className='flex items-center justify-center w-8 h-6 rounded hover:bg-base-300 transition-colors group'
            onClick={handleMinimize}
            title='Minimize'
          >
            <svg
              className='w-3 h-3 text-base-content/70 group-hover:text-base-content'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M20 12H4' />
            </svg>
          </button>

          {/* Maximize/Restore button */}
          <button
            className='flex items-center justify-center w-8 h-6 rounded hover:bg-base-300 transition-colors group'
            onClick={handleMaximize}
            title={isMaximized ? 'Restore' : 'Maximize'}
          >
            {isMaximized ? (
              <svg
                className='w-3 h-3 text-base-content/70 group-hover:text-base-content'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M8 7V3m0 0l3 3m-3-3l-3 3m5 8h3m-3 0v3m0-3l3-3m-3 3l-3-3'
                />
              </svg>
            ) : (
              <svg
                className='w-3 h-3 text-base-content/70 group-hover:text-base-content'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4'
                />
              </svg>
            )}
          </button>

          {/* Close button */}
          <button
            className='flex items-center justify-center w-8 h-6 rounded hover:bg-error hover:text-error-content transition-colors group'
            onClick={handleClose}
            title='Close'
          >
            <svg
              className='w-3 h-3 text-base-content/70 group-hover:text-error-content'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M6 18L18 6M6 6l12 12'
              />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

export default TitleBar;
