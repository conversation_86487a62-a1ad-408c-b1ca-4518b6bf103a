import React, { useState, useCallback, useEffect } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { motion, AnimatePresence } from 'framer-motion';
import { useHotkeys } from 'react-hotkeys-hook';
import TitleBar from './TitleBar';
import Sidebar from './Sidebar';
import MainContent from './MainContent';
import StatusBar, { useStatusBar } from './StatusBar';

interface AppLayoutProps {
  children?: React.ReactNode;
  title?: string;
  showSidebar?: boolean;
  showStatusBar?: boolean;
  showRightPanel?: boolean;
  rightPanelContent?: React.ReactNode;
  sidebarContent?: React.ReactNode;
  className?: string;
}

interface LayoutState {
  sidebarCollapsed: boolean;
  sidebarSize: number;
  fullscreen: boolean;
  zenMode: boolean;
}

const STORAGE_KEY = 'app-layout-state';
const DEFAULT_SIDEBAR_SIZE = 20;
const MIN_SIDEBAR_SIZE = 15;
const MAX_SIDEBAR_SIZE = 40;

export const AppLayout: React.FC<AppLayoutProps> = ({
  children,
  title,
  showSidebar = true,
  showStatusBar = true,
  showRightPanel = false,
  rightPanelContent,
  sidebarContent,
  className = '',
}) => {
  const [layoutState, setLayoutState] = useState<LayoutState>({
    sidebarCollapsed: false,
    sidebarSize: DEFAULT_SIDEBAR_SIZE,
    fullscreen: false,
    zenMode: false,
  });

  const [menuOpen, setMenuOpen] = useState(false);
  const { showStatus } = useStatusBar();

  // Load layout state from localStorage
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        const parsed = JSON.parse(savedState);
        setLayoutState(prev => ({
          ...prev,
          ...parsed,
          sidebarSize: Math.max(
            MIN_SIDEBAR_SIZE,
            Math.min(MAX_SIDEBAR_SIZE, parsed.sidebarSize || DEFAULT_SIDEBAR_SIZE)
          ),
        }));
      }
    } catch (error) {
      console.warn('Failed to load app layout state:', error);
    }
  }, []);

  // Save layout state to localStorage
  const saveLayoutState = useCallback(
    (newState: Partial<LayoutState>) => {
      const updatedState = { ...layoutState, ...newState };
      setLayoutState(updatedState);

      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState));
      } catch (error) {
        console.warn('Failed to save app layout state:', error);
      }
    },
    [layoutState]
  );

  // Handle sidebar resize
  const handleSidebarResize = useCallback(
    (size: number) => {
      if (!layoutState.sidebarCollapsed) {
        saveLayoutState({ sidebarSize: size });
      }
    },
    [layoutState.sidebarCollapsed, saveLayoutState]
  );

  // Toggle sidebar
  const toggleSidebar = useCallback(() => {
    const newCollapsed = !layoutState.sidebarCollapsed;
    saveLayoutState({ sidebarCollapsed: newCollapsed });
    showStatus(newCollapsed ? 'Sidebar hidden' : 'Sidebar shown', 'info', 1000);
  }, [layoutState.sidebarCollapsed, saveLayoutState, showStatus]);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    const newFullscreen = !layoutState.fullscreen;
    saveLayoutState({ fullscreen: newFullscreen });

    if (window.electronAPI && 'windowControls' in window.electronAPI) {
      const electronAPI = window.electronAPI as any;
      if (electronAPI?.windowControls?.setFullscreen) {
        electronAPI.windowControls.setFullscreen(newFullscreen);
      }
    }

    showStatus(newFullscreen ? 'Entered fullscreen' : 'Exited fullscreen', 'info', 1000);
  }, [layoutState.fullscreen, saveLayoutState, showStatus]);

  // Toggle zen mode (hide all UI except content)
  const toggleZenMode = useCallback(() => {
    const newZenMode = !layoutState.zenMode;
    saveLayoutState({ zenMode: newZenMode });
    showStatus(newZenMode ? 'Zen mode enabled' : 'Zen mode disabled', 'info', 1000);
  }, [layoutState.zenMode, saveLayoutState, showStatus]);

  // Handle menu click
  const handleMenuClick = useCallback(() => {
    setMenuOpen(!menuOpen);
  }, [menuOpen]);

  // Close menu when clicking outside
  const handleMenuClose = useCallback(() => {
    setMenuOpen(false);
  }, []);

  // Keyboard shortcuts
  useHotkeys('ctrl+b', toggleSidebar, { preventDefault: true });
  useHotkeys('f11', toggleFullscreen, { preventDefault: true });
  useHotkeys('ctrl+k,ctrl+z', toggleZenMode, { preventDefault: true });
  useHotkeys('alt', () => setMenuOpen(!menuOpen), { preventDefault: true });
  useHotkeys(
    'escape',
    () => {
      if (menuOpen) {
        setMenuOpen(false);
      } else if (layoutState.zenMode) {
        toggleZenMode();
      }
    },
    { preventDefault: true }
  );

  // Menu items
  const menuItems = [
    {
      label: 'File',
      items: [
        {
          label: 'New Document',
          shortcut: 'Ctrl+N',
          action: () => showStatus('New document', 'info'),
        },
        {
          label: 'Open Document',
          shortcut: 'Ctrl+O',
          action: () => showStatus('Open document', 'info'),
        },
        { label: 'Save', shortcut: 'Ctrl+S', action: () => showStatus('Save document', 'info') },
        { label: 'Save As', shortcut: 'Ctrl+Shift+S', action: () => showStatus('Save as', 'info') },
        { type: 'separator' },
        { label: 'Exit', shortcut: 'Ctrl+Q', action: () => window.close() },
      ],
    },
    {
      label: 'Edit',
      items: [
        { label: 'Undo', shortcut: 'Ctrl+Z', action: () => showStatus('Undo', 'info') },
        { label: 'Redo', shortcut: 'Ctrl+Y', action: () => showStatus('Redo', 'info') },
        { type: 'separator' },
        { label: 'Cut', shortcut: 'Ctrl+X', action: () => showStatus('Cut', 'info') },
        { label: 'Copy', shortcut: 'Ctrl+C', action: () => showStatus('Copy', 'info') },
        { label: 'Paste', shortcut: 'Ctrl+V', action: () => showStatus('Paste', 'info') },
      ],
    },
    {
      label: 'View',
      items: [
        {
          label: layoutState.sidebarCollapsed ? 'Show Sidebar' : 'Hide Sidebar',
          shortcut: 'Ctrl+B',
          action: toggleSidebar,
        },
        {
          label: layoutState.fullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen',
          shortcut: 'F11',
          action: toggleFullscreen,
        },
        {
          label: layoutState.zenMode ? 'Exit Zen Mode' : 'Enter Zen Mode',
          shortcut: 'Ctrl+K Ctrl+Z',
          action: toggleZenMode,
        },
        { type: 'separator' },
        { label: 'Zoom In', shortcut: 'Ctrl++', action: () => showStatus('Zoom in', 'info') },
        { label: 'Zoom Out', shortcut: 'Ctrl+-', action: () => showStatus('Zoom out', 'info') },
        { label: 'Reset Zoom', shortcut: 'Ctrl+0', action: () => showStatus('Reset zoom', 'info') },
      ],
    },
    {
      label: 'Help',
      items: [
        { label: 'Documentation', action: () => showStatus('Opening documentation', 'info') },
        { label: 'Keyboard Shortcuts', action: () => showStatus('Showing shortcuts', 'info') },
        { label: 'About', action: () => showStatus('About AI Document Processor', 'info') },
      ],
    },
  ];

  // Render application menu
  const renderMenu = () => (
    <AnimatePresence>
      {menuOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className='fixed inset-0 z-40'
            onClick={handleMenuClose}
          />

          {/* Menu */}
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className='absolute top-8 left-4 z-50 bg-base-100 border border-base-300 rounded-lg shadow-lg min-w-48'
          >
            <div className='py-2'>
              {menuItems.map(menu => (
                <div key={menu.label} className='group'>
                  <div className='px-4 py-2 text-sm font-medium text-base-content/70 hover:bg-base-200 cursor-pointer'>
                    {menu.label}
                  </div>
                  <div className='hidden group-hover:block absolute left-full top-0 bg-base-100 border border-base-300 rounded-lg shadow-lg min-w-48'>
                    <div className='py-2'>
                      {menu.items.map((item, itemIndex) =>
                        'type' in item && item.type === 'separator' ? (
                          <div key={itemIndex} className='border-t border-base-300 my-1' />
                        ) : (
                          <button
                            key={itemIndex}
                            className='w-full px-4 py-2 text-sm text-left text-base-content hover:bg-base-200 flex items-center justify-between'
                            onClick={() => {
                              'action' in item && item.action?.();
                              handleMenuClose();
                            }}
                          >
                            <span>{'label' in item ? item.label : ''}</span>
                            {'shortcut' in item && item.shortcut && (
                              <span className='text-xs text-base-content/50 ml-4'>
                                {item.shortcut}
                              </span>
                            )}
                          </button>
                        )
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );

  // Don't render title bar and status bar in zen mode
  const showTitleBar = !layoutState.zenMode;
  const showStatusBarComponent = showStatusBar && !layoutState.zenMode;
  const showSidebarComponent = showSidebar && !layoutState.zenMode && !layoutState.sidebarCollapsed;

  return (
    <div className={`min-h-screen bg-base-100 text-base-content flex flex-col ${className}`}>
      {/* Application Menu */}
      {renderMenu()}

      {/* Title Bar */}
      {showTitleBar && (
        <TitleBar title={title || 'AI Document Processor'} onMenuClick={handleMenuClick} />
      )}

      {/* Main Layout */}
      <div className='flex-1 flex overflow-hidden'>
        {showSidebarComponent ? (
          <PanelGroup direction='horizontal'>
            <Panel
              defaultSize={layoutState.sidebarSize}
              minSize={MIN_SIDEBAR_SIZE}
              maxSize={MAX_SIDEBAR_SIZE}
              onResize={handleSidebarResize}
            >
              <Sidebar>{sidebarContent}</Sidebar>
            </Panel>
            <PanelResizeHandle className='w-1 bg-base-300 hover:bg-primary transition-colors' />
            <Panel defaultSize={100 - layoutState.sidebarSize} minSize={60}>
              <MainContent showRightPanel={showRightPanel} rightPanelContent={rightPanelContent}>
                {children}
              </MainContent>
            </Panel>
          </PanelGroup>
        ) : (
          <MainContent showRightPanel={showRightPanel} rightPanelContent={rightPanelContent}>
            {children}
          </MainContent>
        )}
      </div>

      {/* Status Bar */}
      {showStatusBarComponent && <StatusBar showProgress={true} showQuickActions={true} />}
    </div>
  );
};

export default AppLayout;
