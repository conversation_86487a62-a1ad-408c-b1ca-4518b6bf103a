import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

// Types for OCR processing
export interface OCROptions {
  language?: string;
  enhanceImage?: boolean;
  detectTables?: boolean;
  detectFormFields?: boolean;
  preserveLayout?: boolean;
  confidenceThreshold?: number;
}

export interface OCRResult {
  text: string;
  confidence: number;
  words: OCRWord[];
  lines: OCRLine[];
  paragraphs: OCRParagraph[];
  tables?: OCRTable[];
  formFields?: OCRFormField[];
  processingTime: number;
}

export interface OCRWord {
  text: string;
  confidence: number;
  bbox: BoundingBox;
}

export interface OCRLine {
  text: string;
  confidence: number;
  words: OCRWord[];
  bbox: BoundingBox;
}

export interface OCRParagraph {
  text: string;
  confidence: number;
  lines: OCRLine[];
  bbox: BoundingBox;
}

export interface OCRTable {
  rows: OCRTableRow[];
  confidence: number;
  bbox: BoundingBox;
}

export interface OCRTableRow {
  cells: OCRTableCell[];
}

export interface OCRTableCell {
  text: string;
  confidence: number;
  bbox: BoundingBox;
}

export interface OCRFormField {
  type: 'text' | 'checkbox' | 'radio' | 'select' | 'signature';
  label?: string;
  value?: string;
  confidence: number;
  bbox: BoundingBox;
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface BatchOCRRequest {
  images: (File | Buffer | string)[];
  options?: OCROptions;
}

export interface BatchOCRResult {
  results: OCRResult[];
  totalProcessingTime: number;
  successCount: number;
  failureCount: number;
  errors: string[];
}

/**
 * Hook for OCR processing operations
 */
export const useOCRProcessing = () => {
  const queryClient = useQueryClient();

  // Query for getting OCR result
  const getOCRResult = (imageId: string) => {
    return useQuery({
      queryKey: ['ocr', 'result', imageId],
      queryFn: async () => {
        // This would call an IPC method to get OCR result
        // For now, we'll simulate the response
        return {
          imageId,
          result: null,
          status: 'pending',
        };
      },
      enabled: !!imageId,
      staleTime: 30 * 60 * 1000, // 30 minutes
    });
  };

  // Mutation for performing OCR on an image
  const performOCR = useMutation({
    mutationKey: ['ocr-process'],
    mutationFn: async ({
      image: _image,
      options: _options = {},
    }: {
      image: File | Buffer | string;
      options?: OCROptions;
    }) => {
      // For now, we'll simulate OCR processing
      // In a real implementation, this would call the OCR worker through IPC

      const startTime = Date.now();

      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      const result: OCRResult = {
        text: 'Sample OCR text extracted from image',
        confidence: 0.95,
        words: [],
        lines: [],
        paragraphs: [],
        processingTime: Date.now() - startTime,
      };

      return result;
    },
    onSuccess: data => {
      toast.success('OCR processing completed successfully!');

      // Cache the result
      queryClient.setQueryData(['ocr', 'result', 'latest'], {
        result: data,
        status: 'completed',
      });
    },
    onError: (error: Error) => {
      toast.error(`OCR processing failed: ${error.message}`);
    },
  });

  // Mutation for batch OCR processing
  const batchOCR = useMutation({
    mutationKey: ['ocr-batch-process'],
    mutationFn: async ({ images, options = {} }: BatchOCRRequest) => {
      const results: OCRResult[] = [];
      const errors: string[] = [];
      let totalProcessingTime = 0;

      for (let i = 0; i < images.length; i++) {
        try {
          const image = images[i];
          if (!image) continue;

          const result = await performOCR.mutateAsync({
            image,
            options,
          });
          results.push(result);
          totalProcessingTime += result.processingTime;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push(`Image ${i + 1}: ${errorMessage}`);
        }
      }

      const batchResult: BatchOCRResult = {
        results,
        totalProcessingTime,
        successCount: results.length,
        failureCount: errors.length,
        errors,
      };

      return batchResult;
    },
    onSuccess: data => {
      const { successCount, failureCount } = data;

      if (failureCount === 0) {
        toast.success(`All ${successCount} images processed successfully!`);
      } else {
        toast.success(`${successCount} images processed, ${failureCount} failed`);
      }
    },
    onError: (error: Error) => {
      toast.error(`Batch OCR processing failed: ${error.message}`);
    },
  });

  // Mutation for enhancing image quality before OCR
  const enhanceImageForOCR = useMutation({
    mutationKey: ['ocr-enhance-image'],
    mutationFn: async ({
      image,
      enhancementOptions = {},
    }: {
      image: File | Buffer;
      enhancementOptions?: {
        denoise?: boolean;
        sharpen?: boolean;
        contrast?: boolean;
        brightness?: number;
        deskew?: boolean;
      };
    }) => {
      // This would call image enhancement service through IPC
      // For now, we'll simulate the enhancement

      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        enhancedImage: image, // In reality, this would be the enhanced image
        enhancementApplied: Object.keys(enhancementOptions),
        qualityScore: 0.92,
      };
    },
    onSuccess: () => {
      toast.success('Image enhanced for OCR processing!');
    },
    onError: (error: Error) => {
      toast.error(`Image enhancement failed: ${error.message}`);
    },
  });

  // Mutation for extracting tables from OCR result
  const extractTables = useMutation({
    mutationKey: ['ocr-extract-tables'],
    mutationFn: async ({
      ocrResult: _ocrResult,
      tableOptions: _tableOptions = {},
    }: {
      ocrResult: OCRResult;
      tableOptions?: {
        minRows?: number;
        minColumns?: number;
        confidenceThreshold?: number;
      };
    }) => {
      // This would process the OCR result to extract table structures
      // For now, we'll simulate table extraction

      await new Promise(resolve => setTimeout(resolve, 500));

      const tables: OCRTable[] = [
        {
          rows: [
            {
              cells: [
                {
                  text: 'Header 1',
                  confidence: 0.95,
                  bbox: { x: 0, y: 0, width: 100, height: 20 },
                },
                {
                  text: 'Header 2',
                  confidence: 0.93,
                  bbox: { x: 100, y: 0, width: 100, height: 20 },
                },
              ],
            },
            {
              cells: [
                { text: 'Data 1', confidence: 0.91, bbox: { x: 0, y: 20, width: 100, height: 20 } },
                {
                  text: 'Data 2',
                  confidence: 0.89,
                  bbox: { x: 100, y: 20, width: 100, height: 20 },
                },
              ],
            },
          ],
          confidence: 0.92,
          bbox: { x: 0, y: 0, width: 200, height: 40 },
        },
      ];

      return {
        tables,
        tableCount: tables.length,
        averageConfidence: tables.reduce((sum, table) => sum + table.confidence, 0) / tables.length,
      };
    },
    onSuccess: data => {
      toast.success(`${data.tableCount} tables extracted successfully!`);
    },
    onError: (error: Error) => {
      toast.error(`Table extraction failed: ${error.message}`);
    },
  });

  return {
    // Queries
    getOCRResult,

    // Mutations
    performOCR,
    batchOCR,
    enhanceImageForOCR,
    extractTables,

    // Loading states
    isProcessing: performOCR.isPending,
    isBatchProcessing: batchOCR.isPending,
    isEnhancing: enhanceImageForOCR.isPending,
    isExtractingTables: extractTables.isPending,

    // Error states
    ocrError: performOCR.error || batchOCR.error || enhanceImageForOCR.error || extractTables.error,

    // Progress tracking
    batchProgress: batchOCR.data
      ? {
          completed: batchOCR.data.successCount + batchOCR.data.failureCount,
          total: batchOCR.data.successCount + batchOCR.data.failureCount,
          percentage: 100,
        }
      : null,
  };
};
