import { useMutation, useQuery } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

// Types for entity extraction
export interface Entity {
  id: string;
  text: string;
  type: EntityType;
  confidence: number;
  startOffset: number;
  endOffset: number;
  metadata?: {
    normalized?: string;
    category?: string;
    subtype?: string;
    context?: string;
    [key: string]: any;
  };
}

export type EntityType =
  | 'PERSON'
  | 'ORGANIZATION'
  | 'LOCATION'
  | 'DATE'
  | 'TIME'
  | 'MONEY'
  | 'PERCENTAGE'
  | 'EMAIL'
  | 'PHONE'
  | 'URL'
  | 'SSN'
  | 'CREDIT_CARD'
  | 'ADDRESS'
  | 'CUSTOM';

export interface EntityExtractionRequest {
  text: string;
  entityTypes?: EntityType[];
  language?: string;
  confidenceThreshold?: number;
  includeContext?: boolean;
  customPatterns?: CustomPattern[];
}

export interface CustomPattern {
  name: string;
  pattern: string;
  type: EntityType;
  flags?: string;
}

export interface EntityExtractionResponse {
  entities: Entity[];
  totalEntities: number;
  entityTypes: { [key in EntityType]?: number };
  processingTime: number;
  language: string;
  confidence: number;
}

export interface BatchEntityExtractionRequest {
  texts: string[];
  entityTypes?: EntityType[];
  language?: string;
  confidenceThreshold?: number;
  batchSize?: number;
}

export interface BatchEntityExtractionResponse {
  results: EntityExtractionResponse[];
  totalTexts: number;
  totalEntities: number;
  averageConfidence: number;
  processingTime: number;
}

export interface EntityRelationship {
  entity1: Entity;
  entity2: Entity;
  relationship: string;
  confidence: number;
  context: string;
}

export interface EntityAnalysisRequest {
  entities: Entity[];
  analysisType: 'frequency' | 'cooccurrence' | 'sentiment' | 'relationships';
  options?: {
    windowSize?: number;
    minFrequency?: number;
    includeContext?: boolean;
  };
}

export interface EntityAnalysisResponse {
  analysisType: string;
  results: any;
  metadata: {
    totalEntities: number;
    processingTime: number;
    confidence: number;
  };
}

export interface EntityValidationRequest {
  entities: Entity[];
  validationRules?: ValidationRule[];
}

export interface ValidationRule {
  entityType: EntityType;
  rule: 'format' | 'length' | 'pattern' | 'lookup' | 'custom';
  parameters: any;
}

export interface EntityValidationResponse {
  validatedEntities: (Entity & { isValid: boolean; validationErrors?: string[] })[];
  totalValidated: number;
  validCount: number;
  invalidCount: number;
}

// Helper function to get electronAPI
// const _getElectronAPI = () => {
//   if (typeof window !== 'undefined' && window.electronAPI) {
//     return window.electronAPI;
//   }
//   throw new Error('ElectronAPI not available');
// };

/**
 * Hook for entity extraction and NLP operations
 */
export const useEntityExtraction = () => {
  // Query for getting cached entity extraction results
  const getCachedEntities = (textHash: string) => {
    return useQuery({
      queryKey: ['entity-extraction', 'cache', textHash],
      queryFn: async () => {
        // Check if entities are cached
        return null;
      },
      enabled: !!textHash,
      staleTime: 30 * 60 * 1000, // 30 minutes
    });
  };

  // Mutation for extracting entities from text
  const extractEntities = useMutation({
    mutationKey: ['entity-extraction'],
    mutationFn: async (request: EntityExtractionRequest) => {
      const startTime = Date.now();

      // Simulate entity extraction
      // In a real implementation, this would call NLP services through IPC

      const entities: Entity[] = [];

      // Simple regex-based extraction for demonstration
      const patterns = {
        EMAIL: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
        PHONE: /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g,
        DATE: /\b\d{1,2}\/\d{1,2}\/\d{4}\b/g,
        MONEY: /\$\d+(?:,\d{3})*(?:\.\d{2})?/g,
        PERCENTAGE: /\d+(?:\.\d+)?%/g,
      };

      // Extract entities based on patterns
      Object.entries(patterns).forEach(([type, pattern]) => {
        if (!request.entityTypes || request.entityTypes.includes(type as EntityType)) {
          let match;
          while ((match = pattern.exec(request.text)) !== null) {
            entities.push({
              id: `entity_${entities.length + 1}`,
              text: match[0],
              type: type as EntityType,
              confidence: 0.85 + Math.random() * 0.1,
              startOffset: match.index,
              endOffset: match.index + match[0].length,
              metadata: {
                normalized: match[0].toLowerCase(),
                context: request.text.substring(
                  Math.max(0, match.index - 20),
                  Math.min(request.text.length, match.index + match[0].length + 20)
                ),
              },
            });
          }
        }
      });

      // Add some simulated named entities
      if (!request.entityTypes || request.entityTypes.includes('PERSON')) {
        entities.push({
          id: `entity_${entities.length + 1}`,
          text: 'John Doe',
          type: 'PERSON',
          confidence: 0.92,
          startOffset: request.text.indexOf('John Doe'),
          endOffset: request.text.indexOf('John Doe') + 8,
          metadata: {
            normalized: 'john doe',
            category: 'person_name',
          },
        });
      }

      // Filter by confidence threshold
      const filteredEntities = entities.filter(
        entity => entity.confidence >= (request.confidenceThreshold || 0.5)
      );

      // Calculate entity type counts
      const entityTypes: { [key in EntityType]?: number } = {};
      filteredEntities.forEach(entity => {
        entityTypes[entity.type] = (entityTypes[entity.type] || 0) + 1;
      });

      const response: EntityExtractionResponse = {
        entities: filteredEntities,
        totalEntities: filteredEntities.length,
        entityTypes,
        processingTime: Date.now() - startTime,
        language: request.language || 'en',
        confidence:
          filteredEntities.reduce((sum, e) => sum + e.confidence, 0) / filteredEntities.length || 0,
      };

      return response;
    },
    onSuccess: data => {
      toast.success(
        `Extracted ${data.totalEntities} entities with ${(data.confidence * 100).toFixed(1)}% confidence`
      );
    },
    onError: (error: Error) => {
      toast.error(`Entity extraction failed: ${error.message}`);
    },
  });

  // Mutation for batch entity extraction
  const batchExtractEntities = useMutation({
    mutationKey: ['entity-extraction-batch'],
    mutationFn: async (request: BatchEntityExtractionRequest) => {
      const startTime = Date.now();
      const batchSize = request.batchSize || 5;
      const results: EntityExtractionResponse[] = [];

      // Process in batches
      for (let i = 0; i < request.texts.length; i += batchSize) {
        const batch = request.texts.slice(i, i + batchSize);

        for (const text of batch) {
          try {
            const result = await extractEntities.mutateAsync({
              text,
              entityTypes: request.entityTypes || [],
              language: request.language || 'en',
              confidenceThreshold: request.confidenceThreshold || 0.5,
            });
            results.push(result);
          } catch (error) {
            console.error(`Failed to extract entities from text ${i + 1}:`, error);
          }
        }

        // Small delay between batches
        if (i + batchSize < request.texts.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      const totalEntities = results.reduce((sum, r) => sum + r.totalEntities, 0);
      const averageConfidence =
        results.reduce((sum, r) => sum + r.confidence, 0) / results.length || 0;

      const response: BatchEntityExtractionResponse = {
        results,
        totalTexts: request.texts.length,
        totalEntities,
        averageConfidence,
        processingTime: Date.now() - startTime,
      };

      return response;
    },
    onSuccess: data => {
      toast.success(
        `Batch extraction completed: ${data.totalEntities} entities from ${data.totalTexts} texts`
      );
    },
    onError: (error: Error) => {
      toast.error(`Batch entity extraction failed: ${error.message}`);
    },
  });

  // Mutation for analyzing entity relationships
  const analyzeEntityRelationships = useMutation({
    mutationKey: ['entity-relationship-analysis'],
    mutationFn: async ({
      entities,
      windowSize = 50,
    }: {
      entities: Entity[];
      windowSize?: number;
    }) => {
      // Find entities that appear close to each other
      const relationships: EntityRelationship[] = [];

      for (let i = 0; i < entities.length; i++) {
        for (let j = i + 1; j < entities.length; j++) {
          const entity1 = entities[i];
          const entity2 = entities[j];

          if (!entity1 || !entity2) continue;

          // Check if entities are within the window size
          const distance = Math.abs(entity1.startOffset - entity2.startOffset);
          if (distance <= windowSize) {
            relationships.push({
              entity1,
              entity2,
              relationship: 'co_occurs',
              confidence: 0.8 - (distance / windowSize) * 0.3,
              context: entity1.metadata?.context || '',
            });
          }
        }
      }

      return {
        relationships,
        totalRelationships: relationships.length,
        windowSize,
        processingTime: 500,
      };
    },
    onSuccess: data => {
      toast.success(`Found ${data.totalRelationships} entity relationships`);
    },
    onError: (error: Error) => {
      toast.error(`Entity relationship analysis failed: ${error.message}`);
    },
  });

  // Mutation for entity validation
  const validateEntities = useMutation({
    mutationKey: ['entity-validation'],
    mutationFn: async (request: EntityValidationRequest) => {
      const validatedEntities = request.entities.map(entity => {
        const validationErrors: string[] = [];
        let isValid = true;

        // Basic validation rules
        switch (entity.type) {
          case 'EMAIL':
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(entity.text)) {
              isValid = false;
              validationErrors.push('Invalid email format');
            }
            break;

          case 'PHONE':
            if (!/^\d{3}[-.]?\d{3}[-.]?\d{4}$/.test(entity.text)) {
              isValid = false;
              validationErrors.push('Invalid phone format');
            }
            break;

          case 'DATE':
            if (!/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(entity.text)) {
              isValid = false;
              validationErrors.push('Invalid date format');
            }
            break;

          default:
            // No specific validation for other types
            break;
        }

        // Check confidence threshold
        if (entity.confidence < 0.7) {
          validationErrors.push('Low confidence score');
        }

        return {
          ...entity,
          isValid: isValid && validationErrors.length === 0,
          validationErrors: validationErrors.length > 0 ? validationErrors : [],
        };
      });

      const validCount = validatedEntities.filter(e => e.isValid).length;
      const invalidCount = validatedEntities.length - validCount;

      const response: EntityValidationResponse = {
        validatedEntities,
        totalValidated: validatedEntities.length,
        validCount,
        invalidCount,
      };

      return response;
    },
    onSuccess: data => {
      toast.success(
        `Validation completed: ${data.validCount}/${data.totalValidated} entities valid`
      );
    },
    onError: (error: Error) => {
      toast.error(`Entity validation failed: ${error.message}`);
    },
  });

  // Mutation for entity frequency analysis
  const analyzeEntityFrequency = useMutation({
    mutationKey: ['entity-frequency-analysis'],
    mutationFn: async (entities: Entity[]) => {
      // Count entity frequencies
      const frequencyMap = new Map<string, { count: number; entities: Entity[] }>();

      entities.forEach(entity => {
        const key = `${entity.type}:${entity.metadata?.normalized || entity.text.toLowerCase()}`;
        const existing = frequencyMap.get(key);

        if (existing) {
          existing.count++;
          existing.entities.push(entity);
        } else {
          frequencyMap.set(key, { count: 1, entities: [entity] });
        }
      });

      // Convert to sorted array
      const frequencies = Array.from(frequencyMap.entries())
        .map(([key, data]) => ({
          key,
          type: key.split(':')[0] as EntityType,
          text: key.split(':')[1],
          count: data.count,
          entities: data.entities,
          frequency: data.count / entities.length,
        }))
        .sort((a, b) => b.count - a.count);

      return {
        frequencies,
        totalUniqueEntities: frequencies.length,
        totalEntities: entities.length,
        mostFrequent: frequencies[0],
        averageFrequency: frequencies.reduce((sum, f) => sum + f.count, 0) / frequencies.length,
      };
    },
    onSuccess: data => {
      toast.success(`Frequency analysis completed: ${data.totalUniqueEntities} unique entities`);
    },
    onError: (error: Error) => {
      toast.error(`Entity frequency analysis failed: ${error.message}`);
    },
  });

  return {
    // Queries
    getCachedEntities,

    // Mutations
    extractEntities,
    batchExtractEntities,
    analyzeEntityRelationships,
    validateEntities,
    analyzeEntityFrequency,

    // Loading states
    isExtracting: extractEntities.isPending,
    isBatchExtracting: batchExtractEntities.isPending,
    isAnalyzingRelationships: analyzeEntityRelationships.isPending,
    isValidating: validateEntities.isPending,
    isAnalyzingFrequency: analyzeEntityFrequency.isPending,

    // Error states
    extractionError:
      extractEntities.error ||
      batchExtractEntities.error ||
      analyzeEntityRelationships.error ||
      validateEntities.error ||
      analyzeEntityFrequency.error,

    // Data
    lastExtraction: extractEntities.data,
    lastBatchExtraction: batchExtractEntities.data,
    lastRelationshipAnalysis: analyzeEntityRelationships.data,
    lastValidation: validateEntities.data,
    lastFrequencyAnalysis: analyzeEntityFrequency.data,
  };
};
