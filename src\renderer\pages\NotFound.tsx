import React from 'react';
import { useAppNavigation } from '../router/RouterProvider';
import { AppLayout } from '../components/layout';

const NotFound: React.FC = () => {
  const { navigate } = useAppNavigation();

  return (
    <AppLayout title='Page Not Found'>
      <div className='h-full flex items-center justify-center p-8'>
        <div className='text-center max-w-md'>
          <div className='mb-8'>
            <div className='w-24 h-24 bg-error/10 rounded-full flex items-center justify-center mx-auto mb-6'>
              <svg
                className='w-12 h-12 text-error'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                />
              </svg>
            </div>
            <h1 className='text-4xl font-bold text-base-content mb-4'>404</h1>
            <h2 className='text-xl font-semibold text-base-content mb-2'>Page Not Found</h2>
            <p className='text-base-content/70 mb-8'>
              The page you're looking for doesn't exist or has been moved.
            </p>
          </div>

          <div className='space-y-3'>
            <button className='btn btn-primary btn-block' onClick={() => navigate('/')}>
              Go to Dashboard
            </button>
            <button className='btn btn-outline btn-block' onClick={() => window.history.back()}>
              Go Back
            </button>
          </div>

          <div className='mt-8 text-xs text-base-content/50'>
            If you believe this is an error, please contact support.
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default NotFound;
