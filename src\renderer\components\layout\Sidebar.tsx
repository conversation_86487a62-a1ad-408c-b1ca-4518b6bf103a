import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation } from 'react-router-dom';
import { useAppNavigation } from '../../router/RouterProvider';
import { navigationItems, NavigationItem } from '../../router/routes';

interface SidebarProps {
  defaultSize?: number;
  minSize?: number;
  maxSize?: number;
  collapsible?: boolean;
  className?: string;
  children?: React.ReactNode;
}

interface SidebarState {
  isCollapsed: boolean;
  size: number;
  activePanel: string;
}

const STORAGE_KEY = 'sidebar-state';
const DEFAULT_SIZE = 20; // 20% of the screen width
const MIN_SIZE = 15;
const MAX_SIZE = 40;

export const Sidebar: React.FC<SidebarProps> = ({
  defaultSize = DEFAULT_SIZE,
  minSize = MIN_SIZE,
  maxSize = MAX_SIZE,
  collapsible = true,
  className = '',
  children,
}) => {
  const location = useLocation();
  const { navigate } = useAppNavigation();

  const [sidebarState, setSidebarState] = useState<SidebarState>({
    isCollapsed: false,
    size: defaultSize,
    activePanel: 'explorer',
  });

  // Load sidebar state from localStorage
  useEffect(() => {
    try {
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        const parsed = JSON.parse(savedState);
        setSidebarState(prev => ({
          ...prev,
          ...parsed,
          size: Math.max(minSize, Math.min(maxSize, parsed.size || defaultSize)),
        }));
      }
    } catch (error) {
      console.warn('Failed to load sidebar state:', error);
    }
  }, [defaultSize, minSize, maxSize]);

  // Save sidebar state to localStorage
  const saveSidebarState = useCallback(
    (newState: Partial<SidebarState>) => {
      const updatedState = { ...sidebarState, ...newState };
      setSidebarState(updatedState);

      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedState));
      } catch (error) {
        console.warn('Failed to save sidebar state:', error);
      }
    },
    [sidebarState]
  );

  // Remove unused handleResize function since we're not using react-resizable-panels here
  // const handleResize = useCallback(
  //   (size: number) => {
  //     if (!sidebarState.isCollapsed) {
  //       saveSidebarState({ size });
  //     }
  //   },
  //   [sidebarState.isCollapsed, saveSidebarState]
  // );

  const toggleCollapse = useCallback(() => {
    saveSidebarState({ isCollapsed: !sidebarState.isCollapsed });
  }, [sidebarState.isCollapsed, saveSidebarState]);

  const setActivePanel = useCallback(
    (panel: string) => {
      saveSidebarState({ activePanel: panel });
    },
    [saveSidebarState]
  );

  const handleNavigation = useCallback(
    (path: string) => {
      navigate(path);
    },
    [navigate]
  );

  const isActiveRoute = useCallback(
    (path: string) => {
      if (path === '/') {
        return location.pathname === '/';
      }
      return location.pathname.startsWith(path);
    },
    [location.pathname]
  );

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const isActive = isActiveRoute(item.path);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.path} className={`${level > 0 ? 'ml-4' : ''}`}>
        <button
          className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
            isActive
              ? 'bg-primary text-primary-content shadow-sm'
              : 'text-base-content/80 hover:bg-base-300 hover:text-base-content'
          } ${item.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={() => !item.disabled && handleNavigation(item.path)}
          disabled={item.disabled}
          title={item.label}
        >
          <span className='material-icons text-lg'>{item.icon}</span>
          <AnimatePresence>
            {!sidebarState.isCollapsed && (
              <motion.span
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: 'auto' }}
                exit={{ opacity: 0, width: 0 }}
                transition={{ duration: 0.2 }}
                className='flex-1 text-left truncate'
              >
                {item.label}
              </motion.span>
            )}
          </AnimatePresence>
          {item.badge && !sidebarState.isCollapsed && (
            <span className='badge badge-primary badge-sm'>{item.badge}</span>
          )}
        </button>

        {hasChildren && !sidebarState.isCollapsed && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className='mt-1 space-y-1'
          >
            {item.children?.map(child => renderNavigationItem(child, level + 1))}
          </motion.div>
        )}
      </div>
    );
  };

  const sidebarPanels = [
    {
      id: 'explorer',
      label: 'Explorer',
      icon: 'folder',
      content: (
        <div className='space-y-1'>{navigationItems.map(item => renderNavigationItem(item))}</div>
      ),
    },
    {
      id: 'search',
      label: 'Search',
      icon: 'search',
      content: (
        <div className='p-4'>
          <div className='relative'>
            <input
              type='text'
              placeholder='Search files...'
              className='input input-sm w-full pl-8'
            />
            <span className='material-icons absolute left-2 top-1/2 transform -translate-y-1/2 text-base-content/50 text-sm'>
              search
            </span>
          </div>
        </div>
      ),
    },
    {
      id: 'timeline',
      label: 'Timeline',
      icon: 'timeline',
      content: <div className='p-4 text-sm text-base-content/70'>Timeline view coming soon...</div>,
    },
  ];

  return (
    <div className={`h-full bg-base-200 border-r border-base-300 ${className}`}>
      {/* Sidebar Header */}
      <div className='h-12 border-b border-base-300 flex items-center justify-between px-3'>
        <AnimatePresence>
          {!sidebarState.isCollapsed && (
            <motion.div
              initial={{ opacity: 0, width: 0 }}
              animate={{ opacity: 1, width: 'auto' }}
              exit={{ opacity: 0, width: 0 }}
              transition={{ duration: 0.2 }}
              className='flex items-center space-x-2'
            >
              <span className='text-sm font-semibold text-base-content'>Explorer</span>
            </motion.div>
          )}
        </AnimatePresence>

        {collapsible && (
          <button
            className='p-1 rounded hover:bg-base-300 transition-colors'
            onClick={toggleCollapse}
            title={sidebarState.isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            <span className='material-icons text-lg text-base-content/70'>
              {sidebarState.isCollapsed ? 'chevron_right' : 'chevron_left'}
            </span>
          </button>
        )}
      </div>

      {/* Panel Tabs */}
      <div className='border-b border-base-300'>
        <div className='flex'>
          {sidebarPanels.map(panel => (
            <button
              key={panel.id}
              className={`flex-1 p-2 text-xs transition-colors ${
                sidebarState.activePanel === panel.id
                  ? 'bg-base-100 text-base-content border-b-2 border-primary'
                  : 'text-base-content/70 hover:text-base-content hover:bg-base-300'
              }`}
              onClick={() => setActivePanel(panel.id)}
              title={panel.label}
            >
              <span className='material-icons text-sm'>{panel.icon}</span>
              {!sidebarState.isCollapsed && <div className='mt-1'>{panel.label}</div>}
            </button>
          ))}
        </div>
      </div>

      {/* Panel Content */}
      <div className='flex-1 overflow-y-auto'>
        <AnimatePresence mode='wait'>
          <motion.div
            key={sidebarState.activePanel}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.2 }}
            className='p-3'
          >
            {sidebarPanels.find(p => p.id === sidebarState.activePanel)?.content}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Custom content */}
      {children && <div className='border-t border-base-300'>{children}</div>}
    </div>
  );
};

export default Sidebar;
