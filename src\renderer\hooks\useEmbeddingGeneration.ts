import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '../components/providers/QueryProvider';
import { toast } from 'react-hot-toast';

// Types for embedding generation
export interface EmbeddingRequest {
  text: string;
  model?: string;
  dimensions?: number;
  normalize?: boolean;
}

export interface EmbeddingResponse {
  embedding: number[];
  model: string;
  dimensions: number;
  usage: {
    promptTokens: number;
    totalTokens: number;
  };
  processingTime: number;
}

export interface BatchEmbeddingRequest {
  texts: string[];
  model?: string;
  dimensions?: number;
  normalize?: boolean;
  batchSize?: number;
}

export interface BatchEmbeddingResponse {
  embeddings: number[][];
  model: string;
  dimensions: number;
  totalTexts: number;
  successCount: number;
  failureCount: number;
  totalProcessingTime: number;
  averageProcessingTime: number;
}

export interface SimilarityRequest {
  embedding1: number[];
  embedding2: number[];
  metric?: 'cosine' | 'euclidean' | 'dot_product';
}

export interface SimilarityResponse {
  similarity: number;
  metric: string;
  processingTime: number;
}

export interface EmbeddingCache {
  text: string;
  embedding: number[];
  model: string;
  timestamp: Date;
  hash: string;
}

// Helper function to get electronAPI
const getElectronAPI = () => {
  if (typeof window !== 'undefined' && window.electronAPI) {
    return window.electronAPI;
  }
  throw new Error('ElectronAPI not available');
};

// Helper function to calculate text hash for caching
const calculateTextHash = (text: string): string => {
  let hash = 0;
  for (let i = 0; i < text.length; i++) {
    const char = text.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString();
};

/**
 * Hook for embedding generation and vector operations
 */
export const useEmbeddingGeneration = () => {
  const queryClient = useQueryClient();

  // Query for getting cached embedding
  const getCachedEmbedding = (text: string, model = 'default') => {
    const textHash = calculateTextHash(text);

    return useQuery({
      queryKey: queryKeys.aiEmbedding(`${textHash}_${model}`),
      queryFn: async () => {
        // Check if embedding is cached
        const cached = queryClient.getQueryData<EmbeddingCache>(
          queryKeys.aiEmbedding(`${textHash}_${model}`)
        );

        if (cached && cached.text === text) {
          return cached;
        }

        return null;
      },
      enabled: !!text,
      staleTime: 24 * 60 * 60 * 1000, // 24 hours
    });
  };

  // Mutation for generating embeddings
  const generateEmbedding = useMutation({
    mutationKey: ['ai-generate-embedding'],
    mutationFn: async (request: EmbeddingRequest) => {
      const electronAPI = getElectronAPI();
      const startTime = Date.now();

      // Check cache first
      const textHash = calculateTextHash(request.text);
      const cacheKey = queryKeys.aiEmbedding(`${textHash}_${request.model || 'default'}`);
      const cached = queryClient.getQueryData<EmbeddingCache>(cacheKey);

      if (cached && cached.text === request.text) {
        return {
          embedding: cached.embedding,
          model: cached.model,
          dimensions: cached.embedding.length,
          usage: {
            promptTokens: request.text.length / 4,
            totalTokens: request.text.length / 4,
          },
          processingTime: Date.now() - startTime,
        };
      }

      // Generate new embedding
      const embedding = await (electronAPI as any).generateEmbeddings(request.text);

      const response: EmbeddingResponse = {
        embedding: embedding || [],
        model: request.model || 'default',
        dimensions: embedding?.length || 0,
        usage: {
          promptTokens: request.text.length / 4,
          totalTokens: request.text.length / 4,
        },
        processingTime: Date.now() - startTime,
      };

      // Cache the result
      const cacheData: EmbeddingCache = {
        text: request.text,
        embedding: response.embedding,
        model: response.model,
        timestamp: new Date(),
        hash: textHash,
      };

      queryClient.setQueryData(cacheKey, cacheData);

      return response;
    },
    onSuccess: () => {
      toast.success('Embedding generated successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Embedding generation failed: ${error.message}`);
    },
  });

  // Mutation for batch embedding generation
  const generateBatchEmbeddings = useMutation({
    mutationKey: ['ai-generate-batch-embeddings'],
    mutationFn: async (request: BatchEmbeddingRequest) => {
      const startTime = Date.now();
      const batchSize = request.batchSize || 10;
      const embeddings: number[][] = [];
      let successCount = 0;
      let failureCount = 0;

      // Process in batches
      for (let i = 0; i < request.texts.length; i += batchSize) {
        const batch = request.texts.slice(i, i + batchSize);

        for (const text of batch) {
          try {
            const result = await generateEmbedding.mutateAsync({
              text,
              model: request.model || 'text-embedding-ada-002',
              dimensions: request.dimensions || 1536,
              normalize: request.normalize || false,
            });
            embeddings.push(result.embedding);
            successCount++;
          } catch (error) {
            embeddings.push([]); // Empty embedding for failed texts
            failureCount++;
          }
        }

        // Small delay between batches to avoid overwhelming the API
        if (i + batchSize < request.texts.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      const totalProcessingTime = Date.now() - startTime;

      const response: BatchEmbeddingResponse = {
        embeddings,
        model: request.model || 'default',
        dimensions: embeddings[0]?.length || 0,
        totalTexts: request.texts.length,
        successCount,
        failureCount,
        totalProcessingTime,
        averageProcessingTime: totalProcessingTime / request.texts.length,
      };

      return response;
    },
    onSuccess: data => {
      toast.success(
        `Batch embeddings generated! ${data.successCount}/${data.totalTexts} successful.`
      );
    },
    onError: (error: Error) => {
      toast.error(`Batch embedding generation failed: ${error.message}`);
    },
  });

  // Mutation for calculating similarity between embeddings
  const calculateSimilarity = useMutation({
    mutationKey: ['ai-calculate-similarity'],
    mutationFn: async (request: SimilarityRequest) => {
      const startTime = Date.now();
      const { embedding1, embedding2, metric = 'cosine' } = request;

      let similarity: number;

      switch (metric) {
        case 'cosine':
          // Cosine similarity
          const dotProduct = embedding1.reduce((sum, a, i) => sum + a * (embedding2[i] || 0), 0);
          const magnitude1 = Math.sqrt(embedding1.reduce((sum, a) => sum + a * a, 0));
          const magnitude2 = Math.sqrt(embedding2.reduce((sum, a) => sum + a * a, 0));
          similarity = dotProduct / (magnitude1 * magnitude2);
          break;

        case 'euclidean':
          // Euclidean distance (converted to similarity)
          const distance = Math.sqrt(
            embedding1.reduce((sum, a, i) => sum + Math.pow(a - (embedding2[i] || 0), 2), 0)
          );
          similarity = 1 / (1 + distance);
          break;

        case 'dot_product':
          // Dot product
          similarity = embedding1.reduce((sum, a, i) => sum + a * (embedding2[i] || 0), 0);
          break;

        default:
          throw new Error(`Unsupported similarity metric: ${metric}`);
      }

      const response: SimilarityResponse = {
        similarity,
        metric,
        processingTime: Date.now() - startTime,
      };

      return response;
    },
    onSuccess: data => {
      toast.success(`Similarity calculated: ${data.similarity.toFixed(4)} (${data.metric})`);
    },
    onError: (error: Error) => {
      toast.error(`Similarity calculation failed: ${error.message}`);
    },
  });

  // Mutation for finding similar embeddings
  const findSimilarEmbeddings = useMutation({
    mutationKey: ['ai-find-similar-embeddings'],
    mutationFn: async ({
      queryEmbedding,
      candidateEmbeddings,
      topK = 5,
      metric = 'cosine',
      threshold = 0.7,
    }: {
      queryEmbedding: number[];
      candidateEmbeddings: { id: string; embedding: number[]; metadata?: any }[];
      topK?: number;
      metric?: 'cosine' | 'euclidean' | 'dot_product';
      threshold?: number;
    }) => {
      const similarities = [];

      for (const candidate of candidateEmbeddings) {
        const similarityResult = await calculateSimilarity.mutateAsync({
          embedding1: queryEmbedding,
          embedding2: candidate.embedding,
          metric,
        });

        if (similarityResult.similarity >= threshold) {
          similarities.push({
            id: candidate.id,
            similarity: similarityResult.similarity,
            metadata: candidate.metadata,
          });
        }
      }

      // Sort by similarity (descending) and take top K
      const topSimilar = similarities.sort((a, b) => b.similarity - a.similarity).slice(0, topK);

      return {
        results: topSimilar,
        totalCandidates: candidateEmbeddings.length,
        totalMatches: similarities.length,
        topK,
        threshold,
        metric,
      };
    },
    onSuccess: data => {
      toast.success(`Found ${data.results.length} similar embeddings`);
    },
    onError: (error: Error) => {
      toast.error(`Similar embedding search failed: ${error.message}`);
    },
  });

  // Utility function to clear embedding cache
  const clearEmbeddingCache = () => {
    queryClient.removeQueries({ queryKey: ['ai', 'embedding'] });
    toast.success('Embedding cache cleared');
  };

  return {
    // Queries
    getCachedEmbedding,

    // Mutations
    generateEmbedding,
    generateBatchEmbeddings,
    calculateSimilarity,
    findSimilarEmbeddings,

    // Utilities
    clearEmbeddingCache,

    // Loading states
    isGenerating: generateEmbedding.isPending,
    isBatchGenerating: generateBatchEmbeddings.isPending,
    isCalculatingSimilarity: calculateSimilarity.isPending,
    isFindingSimilar: findSimilarEmbeddings.isPending,

    // Error states
    embeddingError:
      generateEmbedding.error ||
      generateBatchEmbeddings.error ||
      calculateSimilarity.error ||
      findSimilarEmbeddings.error,

    // Data
    lastEmbedding: generateEmbedding.data,
    lastBatchResult: generateBatchEmbeddings.data,
    lastSimilarity: calculateSimilarity.data,
    lastSimilarResults: findSimilarEmbeddings.data,
  };
};
