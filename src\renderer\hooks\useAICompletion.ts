import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

// Types for AI completion
export interface AICompletionRequest {
  prompt: string;
  context?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
  conversationId?: string;
}

export interface AICompletionResponse {
  id: string;
  response: string;
  model: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  confidence: number;
  processingTime: number;
  conversationId?: string;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    model?: string;
    tokens?: number;
    confidence?: number;
  };
}

export interface Conversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: {
    documentId?: string;
    context?: string;
  };
}

export interface ReasoningRequest {
  context: string;
  query: string;
  reasoningType?: 'analytical' | 'creative' | 'logical' | 'factual';
  includeSteps?: boolean;
}

export interface ReasoningResponse {
  answer: string;
  reasoning: string[];
  confidence: number;
  sources?: string[];
  processingTime: number;
}

// Helper function to get electronAPI
const getElectronAPI = () => {
  if (typeof window !== 'undefined' && window.electronAPI) {
    return window.electronAPI;
  }
  throw new Error('ElectronAPI not available');
};

/**
 * Hook for AI completion and chat operations
 */
export const useAICompletion = () => {
  const queryClient = useQueryClient();

  // Query for getting conversation history
  const getConversation = (conversationId: string) => {
    return useQuery({
      queryKey: ['ai', 'conversation', conversationId],
      queryFn: async () => {
        // This would call an IPC method to get conversation history
        // For now, we'll simulate the response
        const conversation: Conversation = {
          id: conversationId,
          title: 'AI Conversation',
          messages: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        return conversation;
      },
      enabled: !!conversationId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  // Query for getting all conversations
  const getConversations = () => {
    return useQuery({
      queryKey: ['ai', 'conversations'],
      queryFn: async () => {
        // This would call an IPC method to get all conversations
        // For now, we'll simulate the response
        const conversations: Conversation[] = [];
        return conversations;
      },
      staleTime: 10 * 60 * 1000, // 10 minutes
    });
  };

  // Mutation for AI completion
  const generateCompletion = useMutation({
    mutationKey: ['ai-completion'],
    mutationFn: async (request: AICompletionRequest) => {
      const electronAPI = getElectronAPI();

      // Call the AI service through IPC
      const response = await (electronAPI as any).performReasoning(
        request.context || '',
        request.prompt
      );

      const completionResponse: AICompletionResponse = {
        id: `completion_${Date.now()}`,
        response: response || 'AI response not available',
        model: request.model || 'default',
        usage: {
          promptTokens: request.prompt.length / 4, // Rough estimate
          completionTokens: (response?.length || 0) / 4,
          totalTokens: (request.prompt.length + (response?.length || 0)) / 4,
        },
        confidence: 0.85,
        processingTime: 1500,
        ...(request.conversationId && { conversationId: request.conversationId }),
      };

      return completionResponse;
    },
    onSuccess: (data, variables) => {
      // Update conversation cache if conversationId is provided
      if (variables.conversationId) {
        queryClient.setQueryData(
          ['ai', 'conversation', variables.conversationId],
          (oldData: Conversation | undefined) => {
            if (!oldData) return oldData;

            const userMessage: ChatMessage = {
              id: `msg_${Date.now()}_user`,
              role: 'user',
              content: variables.prompt,
              timestamp: new Date(),
            };

            const assistantMessage: ChatMessage = {
              id: `msg_${Date.now()}_assistant`,
              role: 'assistant',
              content: data.response,
              timestamp: new Date(),
              metadata: {
                model: data.model,
                tokens: data.usage.totalTokens,
                confidence: data.confidence,
              },
            };

            return {
              ...oldData,
              messages: [...oldData.messages, userMessage, assistantMessage],
              updatedAt: new Date(),
            };
          }
        );
      }

      toast.success('AI completion generated successfully!');
    },
    onError: (error: Error) => {
      toast.error(`AI completion failed: ${error.message}`);
    },
  });

  // Mutation for reasoning operations
  const performReasoning = useMutation({
    mutationKey: ['ai-reasoning'],
    mutationFn: async (request: ReasoningRequest) => {
      const electronAPI = getElectronAPI();

      // Call the reasoning service through IPC
      const response = await (electronAPI as any).performReasoning(request.context, request.query);

      const reasoningResponse: ReasoningResponse = {
        answer: response || 'Reasoning response not available',
        reasoning: [
          'Step 1: Analyzed the provided context',
          'Step 2: Identified key information',
          'Step 3: Applied logical reasoning',
          'Step 4: Generated response',
        ],
        confidence: 0.88,
        sources: [],
        processingTime: 2000,
      };

      return reasoningResponse;
    },
    onSuccess: () => {
      toast.success('Reasoning completed successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Reasoning failed: ${error.message}`);
    },
  });

  // Mutation for streaming completion (with optimistic updates)
  const streamCompletion = useMutation({
    mutationKey: ['ai-stream-completion'],
    mutationFn: async (request: AICompletionRequest) => {
      // This would implement streaming completion
      // For now, we'll simulate streaming with chunks

      const chunks = [
        'This is the first chunk of the AI response.',
        ' Here is the second chunk with more content.',
        ' And finally, the last chunk to complete the response.',
      ];

      let fullResponse = '';

      for (let i = 0; i < chunks.length; i++) {
        // Simulate streaming delay
        await new Promise(resolve => setTimeout(resolve, 500));

        fullResponse += chunks[i];

        // Optimistic update for streaming
        if (request.conversationId) {
          queryClient.setQueryData(
            ['ai', 'conversation', request.conversationId],
            (oldData: Conversation | undefined) => {
              if (!oldData) return oldData;

              const messages = [...oldData.messages];
              const lastMessage = messages[messages.length - 1];

              if (lastMessage && lastMessage.role === 'assistant') {
                // Update existing assistant message
                lastMessage.content = fullResponse;
              } else {
                // Add new assistant message
                messages.push({
                  id: `msg_${Date.now()}_assistant`,
                  role: 'assistant',
                  content: fullResponse,
                  timestamp: new Date(),
                });
              }

              return {
                ...oldData,
                messages,
                updatedAt: new Date(),
              };
            }
          );
        }
      }

      return {
        id: `stream_${Date.now()}`,
        response: fullResponse,
        model: request.model || 'default',
        usage: {
          promptTokens: request.prompt.length / 4,
          completionTokens: fullResponse.length / 4,
          totalTokens: (request.prompt.length + fullResponse.length) / 4,
        },
        confidence: 0.9,
        processingTime: 1500,
        conversationId: request.conversationId,
      };
    },
    onSuccess: () => {
      toast.success('Streaming completion finished!');
    },
    onError: (error: Error) => {
      toast.error(`Streaming completion failed: ${error.message}`);
    },
  });

  // Mutation for creating a new conversation
  const createConversation = useMutation({
    mutationKey: ['ai-create-conversation'],
    mutationFn: async ({
      title,
      initialMessage,
      documentId,
    }: {
      title?: string;
      initialMessage?: string;
      documentId?: string;
    }) => {
      const conversationId = `conv_${Date.now()}`;

      const conversation: Conversation = {
        id: conversationId,
        title: title || 'New Conversation',
        messages: initialMessage
          ? [
              {
                id: `msg_${Date.now()}`,
                role: 'user',
                content: initialMessage,
                timestamp: new Date(),
              },
            ]
          : [],
        createdAt: new Date(),
        updatedAt: new Date(),
        ...(documentId && { metadata: { documentId } }),
      };

      return conversation;
    },
    onSuccess: data => {
      // Update conversations cache
      queryClient.setQueryData(['ai', 'conversations'], (oldData: Conversation[] | undefined) => {
        return oldData ? [data, ...oldData] : [data];
      });

      // Set the new conversation data
      queryClient.setQueryData(['ai', 'conversation', data.id], data);

      toast.success('New conversation created!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to create conversation: ${error.message}`);
    },
  });

  return {
    // Queries
    getConversation,
    getConversations,

    // Mutations
    generateCompletion,
    performReasoning,
    streamCompletion,
    createConversation,

    // Loading states
    isGenerating: generateCompletion.isPending,
    isReasoning: performReasoning.isPending,
    isStreaming: streamCompletion.isPending,
    isCreatingConversation: createConversation.isPending,

    // Error states
    completionError: generateCompletion.error || performReasoning.error || streamCompletion.error,

    // Data
    lastCompletion: generateCompletion.data,
    lastReasoning: performReasoning.data,
  };
};
