// Store exports
export { useDocumentStore, useDocumentSelectors } from './documentStore';
export type { DocumentState, DocumentMetadata, DocumentSortField } from './documentStore';

export { useTabStore, useTabSelectors } from './tabStore';
export type { TabState } from './tabStore';

export { useAIStore, useAISelectors } from './aiStore';
export type { AIState } from './aiStore';

export { useTimelineStore, useTimelineSelectors } from './timelineStore';
export type { TimelineState } from './timelineStore';

export { useUIStore, useUISelectors } from './uiStore';
export type { UIState, DragState } from './uiStore';

// Persistence exports
export { persist, PersistenceManager } from './persistence';
export type {
  PersistenceConfig,
  PersistenceStorage,
  PersistedState,
  BackupEntry,
} from './persistence';

// Store initialization and configuration
import { useDocumentStore } from './documentStore';
import { useTabStore } from './tabStore';
import { useAIStore } from './aiStore';
import { useTimelineStore } from './timelineStore';
import { useUIStore } from './uiStore';

// Initialize stores with persistence
export const initializeStores = () => {
  // Document store persistence is handled internally
  // Tab store persistence is handled internally
  // AI store persistence is handled internally
  // Timeline store persistence is handled internally
  // UI store persistence is handled internally

  console.log('All stores initialized with persistence');
};

// Store reset utility
export const resetAllStores = () => {
  useDocumentStore.getState().reset();
  useTabStore.getState().reset();
  useAIStore.getState().reset();
  useTimelineStore.getState().reset();
  useUIStore.getState().reset();

  console.log('All stores reset to initial state');
};

// Store backup utility
export const createStoreBackups = () => {
  const backupIds: Record<string, string> = {};

  // Create backups for stores that support it
  // Note: Backup functionality would need to be added to each store
  // This is a placeholder for the backup system

  console.log('Store backups created:', backupIds);
  return backupIds;
};

// Store health check utility
export const checkStoreHealth = () => {
  const health = {
    document: true,
    tab: true,
    ai: true,
    timeline: true,
    ui: true,
    timestamp: new Date(),
  };

  try {
    // Check if stores are accessible and have expected structure
    const documentState = useDocumentStore.getState();
    health.document = typeof documentState.documents !== 'undefined';

    const tabState = useTabStore.getState();
    health.tab = Array.isArray(tabState.tabs);

    const aiState = useAIStore.getState();
    health.ai = Array.isArray(aiState.providers);

    const timelineState = useTimelineStore.getState();
    health.timeline = Array.isArray(timelineState.entries);

    const uiState = useUIStore.getState();
    health.ui = typeof uiState.windowBounds !== 'undefined';
  } catch (error) {
    console.error('Store health check failed:', error);
    return {
      ...health,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }

  return health;
};

// Store synchronization utility
export const syncStores = () => {
  // This would handle cross-store synchronization
  // For example, when a document is deleted, remove it from tabs

  const documentStore = useDocumentStore.getState();
  const tabStore = useTabStore.getState();

  // Example: Remove tabs for deleted documents
  const documentIds = new Set(Array.from(documentStore.documents.keys()));
  const tabsToClose = tabStore.tabs.filter(
    (tab: any) => tab.type === 'document' && !documentIds.has(tab.documentId || '')
  );

  tabsToClose.forEach((tab: any) => {
    tabStore.closeTab(tab.id);
  });

  console.log('Stores synchronized');
};

// Store subscription utility for cross-store communication
export const setupStoreSubscriptions = () => {
  // Subscribe to document changes and update tabs accordingly
  useDocumentStore.subscribe(
    (state: any) => state.documents,
    (_documents: any) => {
      // Sync with tab store when documents change
      syncStores();
    }
  );

  // Subscribe to tab changes and update UI accordingly
  useTabStore.subscribe(
    (state: any) => state.activeTabId,
    (activeTabId: any) => {
      const tabStore = useTabStore.getState();
      const activeTab = tabStore.tabs.find((tab: any) => tab.id === activeTabId);

      if (activeTab && activeTab.type === 'document' && activeTab.documentId) {
        // Set active document in document store
        useDocumentStore.getState().setActiveDocument(activeTab.documentId);
      }
    }
  );

  // Subscribe to AI processing state and update UI
  useAIStore.subscribe(
    (state: any) => state.isProcessing,
    (isProcessing: any) => {
      useUIStore.getState().setGlobalLoading(isProcessing, 'Processing AI request...');
    }
  );

  // Subscribe to timeline changes and update UI
  useTimelineStore.subscribe(
    state => state.error,
    error => {
      if (error) {
        useUIStore.getState().setGlobalError(error);
      }
    }
  );

  console.log('Store subscriptions set up');
};

// Store debugging utilities
export const getStoreDebugInfo = () => {
  return {
    document: {
      totalDocuments: useDocumentStore.getState().documents.size,
      activeDocument: useDocumentStore.getState().activeDocumentId,
      selectedCount: useDocumentStore.getState().selectedDocuments.size,
      modifiedCount: useDocumentStore.getState().modifiedDocuments.size,
    },
    tab: {
      totalTabs: useTabStore.getState().tabs.length,
      activeTab: useTabStore.getState().activeTabId,
      totalGroups: useTabStore.getState().groups.length,
      recentlyClosedCount: useTabStore.getState().recentlyClosedTabs.length,
    },
    ai: {
      totalProviders: useAIStore.getState().providers.length,
      activeProvider: useAIStore.getState().activeProviderId,
      totalSessions: useAIStore.getState().activeSessions.length,
      currentSession: useAIStore.getState().currentSessionId,
      isProcessing: useAIStore.getState().isProcessing,
    },
    timeline: {
      totalEntries: useTimelineStore.getState().entries.length,
      totalBranches: useTimelineStore.getState().branches.length,
      selectedBranch: useTimelineStore.getState().selectedBranch?.name,
      canUndo: useTimelineStore.getState().undoRedoState.canUndo,
      canRedo: useTimelineStore.getState().undoRedoState.canRedo,
    },
    ui: {
      totalPanels: useUIStore.getState().panels.length,
      totalModals: useUIStore.getState().modals.length,
      totalToasts: useUIStore.getState().toasts.length,
      totalNotifications: useUIStore.getState().notifications.length,
      colorScheme: useUIStore.getState().colorScheme,
      isFullscreen: useUIStore.getState().isFullscreen,
    },
  };
};

// Export store debug info for development
if (process.env.NODE_ENV === 'development') {
  (window as any).storeDebug = {
    getDebugInfo: getStoreDebugInfo,
    checkHealth: checkStoreHealth,
    resetAll: resetAllStores,
    sync: syncStores,
    createBackups: createStoreBackups,
  };
}

// Auto-initialize stores when module is imported
initializeStores();
setupStoreSubscriptions();
