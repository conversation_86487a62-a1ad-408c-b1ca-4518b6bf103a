import React, { createContext, useContext, useEffect } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { useAppNavigation } from '../../router/RouterProvider';
import { useNotification } from './NotificationProvider';

interface KeyboardShortcut {
  key: string;
  description: string;
  action: () => void;
  category: string;
  global?: boolean;
}

interface KeyboardContextType {
  shortcuts: KeyboardShortcut[];
  registerShortcut: (shortcut: KeyboardShortcut) => void;
  unregisterShortcut: (key: string) => void;
  showShortcuts: () => void;
}

const KeyboardContext = createContext<KeyboardContextType | undefined>(undefined);

/**
 * Keyboard shortcut provider with global hotkey management
 */
export const KeyboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { navigate } = useAppNavigation();
  const { info } = useNotification();
  const [shortcuts, setShortcuts] = React.useState<KeyboardShortcut[]>([]);

  // Global application shortcuts
  const globalShortcuts: KeyboardShortcut[] = [
    {
      key: 'ctrl+n',
      description: 'New document',
      action: () => {
        console.log('New document shortcut triggered');
        info('New document shortcut triggered');
      },
      category: 'Document',
      global: true,
    },
    {
      key: 'ctrl+o',
      description: 'Open document',
      action: () => {
        console.log('Open document shortcut triggered');
        info('Open document shortcut triggered');
      },
      category: 'Document',
      global: true,
    },
    {
      key: 'ctrl+s',
      description: 'Save document',
      action: () => {
        console.log('Save document shortcut triggered');
        info('Save document shortcut triggered');
      },
      category: 'Document',
      global: true,
    },
    {
      key: 'ctrl+shift+s',
      description: 'Save as',
      action: () => {
        console.log('Save as shortcut triggered');
        info('Save as shortcut triggered');
      },
      category: 'Document',
      global: true,
    },
    {
      key: 'ctrl+z',
      description: 'Undo',
      action: () => {
        console.log('Undo shortcut triggered');
        info('Undo shortcut triggered');
      },
      category: 'Edit',
      global: true,
    },
    {
      key: 'ctrl+y',
      description: 'Redo',
      action: () => {
        console.log('Redo shortcut triggered');
        info('Redo shortcut triggered');
      },
      category: 'Edit',
      global: true,
    },
    {
      key: 'ctrl+f',
      description: 'Find',
      action: () => {
        console.log('Find shortcut triggered');
        info('Find shortcut triggered');
      },
      category: 'Search',
      global: true,
    },
    {
      key: 'ctrl+shift+f',
      description: 'Find in files',
      action: () => {
        console.log('Find in files shortcut triggered');
        info('Find in files shortcut triggered');
      },
      category: 'Search',
      global: true,
    },
    {
      key: 'ctrl+1',
      description: 'Go to Dashboard',
      action: () => navigate('/'),
      category: 'Navigation',
      global: true,
    },
    {
      key: 'ctrl+2',
      description: 'Go to Documents',
      action: () => navigate('/documents'),
      category: 'Navigation',
      global: true,
    },
    {
      key: 'ctrl+3',
      description: 'Go to Timeline',
      action: () => navigate('/timeline'),
      category: 'Navigation',
      global: true,
    },
    {
      key: 'ctrl+4',
      description: 'Go to Knowledge Base',
      action: () => navigate('/knowledge'),
      category: 'Navigation',
      global: true,
    },
    {
      key: 'ctrl+comma',
      description: 'Open Settings',
      action: () => navigate('/settings'),
      category: 'Navigation',
      global: true,
    },
    {
      key: 'ctrl+shift+p',
      description: 'Command Palette',
      action: () => {
        console.log('Command palette shortcut triggered');
        info('Command palette shortcut triggered');
      },
      category: 'System',
      global: true,
    },
    {
      key: 'f11',
      description: 'Toggle fullscreen',
      action: () => {
        console.log('Toggle fullscreen shortcut triggered');
        info('Toggle fullscreen shortcut triggered');
      },
      category: 'View',
      global: true,
    },
    {
      key: 'ctrl+shift+i',
      description: 'Toggle Developer Tools',
      action: () => {
        console.log('Toggle Developer Tools shortcut triggered');
        info('Toggle Developer Tools shortcut triggered');
      },
      category: 'Development',
      global: true,
    },
    {
      key: 'ctrl+r',
      description: 'Reload application',
      action: () => {
        window.location.reload();
      },
      category: 'Development',
      global: true,
    },
    {
      key: 'ctrl+shift+r',
      description: 'Hard reload',
      action: () => {
        window.location.reload();
      },
      category: 'Development',
      global: true,
    },
    {
      key: 'ctrl+shift+h',
      description: 'Show keyboard shortcuts',
      action: () => showShortcuts(),
      category: 'Help',
      global: true,
    },
  ];

  // Initialize shortcuts
  useEffect(() => {
    setShortcuts(globalShortcuts);
  }, []);

  // Register global shortcuts
  globalShortcuts.forEach(shortcut => {
    useHotkeys(
      shortcut.key,
      event => {
        event.preventDefault();
        shortcut.action();

        // Log shortcut usage
        if (window.electronAPI?.logToMain) {
          window.electronAPI.logToMain('info', 'Keyboard Shortcut Used', {
            key: shortcut.key,
            description: shortcut.description,
            category: shortcut.category,
            timestamp: new Date().toISOString(),
          });
        }
      },
      {
        enableOnFormTags: false, // Disable on form elements by default
        enableOnContentEditable: false,
      }
    );
  });

  const registerShortcut = (shortcut: KeyboardShortcut) => {
    setShortcuts(prev => {
      const existing = prev.find(s => s.key === shortcut.key);
      if (existing) {
        console.warn(`Shortcut ${shortcut.key} already exists. Overriding.`);
        return prev.map(s => (s.key === shortcut.key ? shortcut : s));
      }
      return [...prev, shortcut];
    });
  };

  const unregisterShortcut = (key: string) => {
    setShortcuts(prev => prev.filter(s => s.key !== key));
  };

  const showShortcuts = () => {
    // Group shortcuts by category
    const groupedShortcuts = shortcuts.reduce(
      (acc, shortcut) => {
        if (!acc[shortcut.category]) {
          acc[shortcut.category] = [];
        }
        acc[shortcut.category]!.push(shortcut);
        return acc;
      },
      {} as Record<string, KeyboardShortcut[]>
    );

    // Create shortcuts display
    let shortcutsText = 'Keyboard Shortcuts:\n\n';
    Object.entries(groupedShortcuts).forEach(([category, categoryShortcuts]) => {
      shortcutsText += `${category}:\n`;
      categoryShortcuts.forEach(shortcut => {
        shortcutsText += `  ${shortcut.key.toUpperCase()}: ${shortcut.description}\n`;
      });
      shortcutsText += '\n';
    });

    console.log(shortcutsText);
    info('Keyboard shortcuts logged to console');
  };

  const value: KeyboardContextType = {
    shortcuts,
    registerShortcut,
    unregisterShortcut,
    showShortcuts,
  };

  return <KeyboardContext.Provider value={value}>{children}</KeyboardContext.Provider>;
};

/**
 * Hook to use keyboard context
 */
export const useKeyboard = () => {
  const context = useContext(KeyboardContext);
  if (context === undefined) {
    throw new Error('useKeyboard must be used within a KeyboardProvider');
  }
  return context;
};

/**
 * Hook to register a temporary shortcut
 */
export const useShortcut = (
  key: string,
  action: () => void,
  description: string,
  category: string = 'Custom',
  deps: React.DependencyList = []
) => {
  const { registerShortcut, unregisterShortcut } = useKeyboard();

  useEffect(() => {
    const shortcut: KeyboardShortcut = {
      key,
      action,
      description,
      category,
    };

    registerShortcut(shortcut);

    return () => {
      unregisterShortcut(key);
    };
  }, deps);
};

/**
 * Keyboard shortcuts help component
 */
export const KeyboardShortcutsHelp: React.FC<{ isOpen: boolean; onClose: () => void }> = ({
  isOpen,
  onClose,
}) => {
  const { shortcuts } = useKeyboard();

  if (!isOpen) return null;

  // Group shortcuts by category
  const groupedShortcuts = shortcuts.reduce(
    (acc, shortcut) => {
      if (!acc[shortcut.category]) {
        acc[shortcut.category] = [];
      }
      acc[shortcut.category]!.push(shortcut);
      return acc;
    },
    {} as Record<string, KeyboardShortcut[]>
  );

  return (
    <div className='fixed inset-0 bg-black/50 flex items-center justify-center z-50'>
      <div className='bg-base-100 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-auto'>
        <div className='flex justify-between items-center mb-4'>
          <h2 className='text-2xl font-bold'>Keyboard Shortcuts</h2>
          <button className='btn btn-ghost btn-sm' onClick={onClose}>
            ✕
          </button>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
            <div key={category}>
              <h3 className='text-lg font-semibold mb-2 text-primary'>{category}</h3>
              <div className='space-y-2'>
                {categoryShortcuts.map(shortcut => (
                  <div key={shortcut.key} className='flex justify-between items-center'>
                    <span className='text-sm'>{shortcut.description}</span>
                    <kbd className='kbd kbd-sm'>{shortcut.key.toUpperCase()}</kbd>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default KeyboardProvider;
