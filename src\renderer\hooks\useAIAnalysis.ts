import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { queryKeys, mutationKeys } from '../components/providers/QueryProvider';
import { toast } from 'react-hot-toast';

// Types for AI analysis
export interface DocumentAnalysisRequest {
  documentId: string;
  analysisType?: AnalysisType[];
  options?: AnalysisOptions;
}

export type AnalysisType =
  | 'content_summary'
  | 'entity_extraction'
  | 'sentiment_analysis'
  | 'key_phrases'
  | 'document_classification'
  | 'form_detection'
  | 'table_analysis'
  | 'image_analysis'
  | 'compliance_check'
  | 'data_validation';

export interface AnalysisOptions {
  language?: string;
  confidenceThreshold?: number;
  includeMetadata?: boolean;
  detailedResults?: boolean;
  customPrompt?: string;
}

export interface DocumentAnalysisResponse {
  documentId: string;
  analysisResults: AnalysisResult[];
  overallConfidence: number;
  processingTime: number;
  model: string;
  timestamp: Date;
}

export interface AnalysisResult {
  type: AnalysisType;
  confidence: number;
  data: any;
  metadata?: {
    model?: string;
    processingTime?: number;
    [key: string]: any;
  };
}

export interface ContentSummary {
  summary: string;
  keyPoints: string[];
  wordCount: number;
  readingTime: number;
  complexity: 'low' | 'medium' | 'high';
}

export interface EntityExtraction {
  entities: Entity[];
  totalEntities: number;
  entityTypes: string[];
}

export interface Entity {
  text: string;
  type: string;
  confidence: number;
  startOffset: number;
  endOffset: number;
  metadata?: any;
}

export interface SentimentAnalysis {
  overall: SentimentScore;
  sentences: SentimentScore[];
  emotions?: EmotionScore[];
}

export interface SentimentScore {
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  score: number; // -1 to 1
}

export interface EmotionScore {
  emotion: string;
  confidence: number;
}

export interface KeyPhrases {
  phrases: KeyPhrase[];
  totalPhrases: number;
  categories: string[];
}

export interface KeyPhrase {
  text: string;
  confidence: number;
  category?: string;
  frequency: number;
}

export interface DocumentClassification {
  categories: ClassificationResult[];
  primaryCategory: string;
  confidence: number;
}

export interface ClassificationResult {
  category: string;
  confidence: number;
  subcategories?: string[];
}

export interface BatchAnalysisRequest {
  documentIds: string[];
  analysisTypes: AnalysisType[];
  options?: AnalysisOptions;
}

export interface BatchAnalysisResponse {
  results: DocumentAnalysisResponse[];
  totalDocuments: number;
  successCount: number;
  failureCount: number;
  totalProcessingTime: number;
  averageProcessingTime: number;
}

// Helper function to get electronAPI
const getElectronAPI = () => {
  if (typeof window !== 'undefined' && window.electronAPI) {
    return window.electronAPI;
  }
  throw new Error('ElectronAPI not available');
};

/**
 * Hook for AI document analysis operations
 */
export const useAIAnalysis = () => {
  const queryClient = useQueryClient();

  // Query for getting analysis results
  const getAnalysisResults = (documentId: string) => {
    return useQuery({
      queryKey: queryKeys.aiAnalysis(documentId),
      queryFn: async () => {
        // This would call an IPC method to get cached analysis results
        // For now, we'll simulate the response
        return null;
      },
      enabled: !!documentId,
      staleTime: 30 * 60 * 1000, // 30 minutes
    });
  };

  // Mutation for analyzing a document
  const analyzeDocument = useMutation({
    mutationKey: mutationKeys.aiAnalysis,
    mutationFn: async (request: DocumentAnalysisRequest) => {
      const startTime = Date.now();
      const analysisTypes = request.analysisType || ['content_summary', 'entity_extraction'];

      // Simulate AI analysis processing
      const analysisResults: AnalysisResult[] = [];

      for (const type of analysisTypes) {
        let data: any;

        switch (type) {
          case 'content_summary':
            data = {
              summary: 'This document contains important information about...',
              keyPoints: [
                'Key point 1: Important finding',
                'Key point 2: Critical information',
                'Key point 3: Notable conclusion',
              ],
              wordCount: 1250,
              readingTime: 5,
              complexity: 'medium',
            } as ContentSummary;
            break;

          case 'entity_extraction':
            data = {
              entities: [
                {
                  text: 'John Doe',
                  type: 'PERSON',
                  confidence: 0.95,
                  startOffset: 10,
                  endOffset: 18,
                },
                {
                  text: 'New York',
                  type: 'LOCATION',
                  confidence: 0.92,
                  startOffset: 45,
                  endOffset: 53,
                },
              ],
              totalEntities: 2,
              entityTypes: ['PERSON', 'LOCATION'],
            } as EntityExtraction;
            break;

          case 'sentiment_analysis':
            data = {
              overall: {
                sentiment: 'positive',
                confidence: 0.85,
                score: 0.7,
              },
              sentences: [
                { sentiment: 'positive', confidence: 0.9, score: 0.8 },
                { sentiment: 'neutral', confidence: 0.8, score: 0.1 },
              ],
            } as SentimentAnalysis;
            break;

          case 'key_phrases':
            data = {
              phrases: [
                { text: 'important document', confidence: 0.9, frequency: 3 },
                { text: 'critical information', confidence: 0.85, frequency: 2 },
              ],
              totalPhrases: 2,
              categories: ['business', 'legal'],
            } as KeyPhrases;
            break;

          case 'document_classification':
            data = {
              categories: [
                { category: 'legal', confidence: 0.9 },
                { category: 'business', confidence: 0.7 },
              ],
              primaryCategory: 'legal',
              confidence: 0.9,
            } as DocumentClassification;
            break;

          default:
            data = { message: `Analysis type ${type} not implemented yet` };
        }

        analysisResults.push({
          type,
          confidence: 0.85 + Math.random() * 0.1,
          data,
          metadata: {
            model: 'gpt-4',
            processingTime: 500 + Math.random() * 1000,
          },
        });
      }

      const response: DocumentAnalysisResponse = {
        documentId: request.documentId,
        analysisResults,
        overallConfidence:
          analysisResults.reduce((sum, r) => sum + r.confidence, 0) / analysisResults.length,
        processingTime: Date.now() - startTime,
        model: 'gpt-4',
        timestamp: new Date(),
      };

      return response;
    },
    onSuccess: data => {
      // Cache the analysis results
      queryClient.setQueryData(queryKeys.aiAnalysis(data.documentId), data);

      toast.success(
        `Document analysis completed! ${data.analysisResults.length} analyses performed.`
      );
    },
    onError: (error: Error) => {
      toast.error(`Document analysis failed: ${error.message}`);
    },
  });

  // Mutation for batch analysis
  const batchAnalyze = useMutation({
    mutationKey: ['ai-batch-analysis'],
    mutationFn: async (request: BatchAnalysisRequest) => {
      const startTime = Date.now();
      const results: DocumentAnalysisResponse[] = [];
      let successCount = 0;
      let failureCount = 0;

      for (const documentId of request.documentIds) {
        try {
          const result = await analyzeDocument.mutateAsync({
            documentId,
            analysisType: request.analysisTypes,
            ...(request.options && { options: request.options }),
          });
          results.push(result);
          successCount++;
        } catch (error) {
          failureCount++;
          console.error(`Analysis failed for document ${documentId}:`, error);
        }
      }

      const totalProcessingTime = Date.now() - startTime;

      const response: BatchAnalysisResponse = {
        results,
        totalDocuments: request.documentIds.length,
        successCount,
        failureCount,
        totalProcessingTime,
        averageProcessingTime: totalProcessingTime / request.documentIds.length,
      };

      return response;
    },
    onSuccess: data => {
      toast.success(
        `Batch analysis completed! ${data.successCount}/${data.totalDocuments} documents analyzed.`
      );
    },
    onError: (error: Error) => {
      toast.error(`Batch analysis failed: ${error.message}`);
    },
  });

  // Mutation for custom analysis with prompt
  const customAnalysis = useMutation({
    mutationKey: ['ai-custom-analysis'],
    mutationFn: async ({
      documentId,
      prompt,
      context,
    }: {
      documentId: string;
      prompt: string;
      context?: string;
    }) => {
      const electronAPI = getElectronAPI();

      // Perform custom analysis using AI reasoning
      const response = await (electronAPI as any).performReasoning(
        context || `Analyze document ${documentId}`,
        prompt
      );

      return {
        documentId,
        prompt,
        response: response || 'Custom analysis response not available',
        confidence: 0.8,
        processingTime: 2000,
        timestamp: new Date(),
      };
    },
    onSuccess: () => {
      toast.success('Custom analysis completed!');
    },
    onError: (error: Error) => {
      toast.error(`Custom analysis failed: ${error.message}`);
    },
  });

  // Mutation for comparing documents
  const compareDocuments = useMutation({
    mutationKey: ['ai-compare-documents'],
    mutationFn: async ({
      documentId1,
      documentId2,
      comparisonType = 'content',
    }: {
      documentId1: string;
      documentId2: string;
      comparisonType?: 'content' | 'structure' | 'sentiment' | 'entities';
    }) => {
      // Get analysis results for both documents
      const analysis1 = await analyzeDocument.mutateAsync({
        documentId: documentId1,
        analysisType: ['content_summary', 'entity_extraction', 'sentiment_analysis'],
      });

      const analysis2 = await analyzeDocument.mutateAsync({
        documentId: documentId2,
        analysisType: ['content_summary', 'entity_extraction', 'sentiment_analysis'],
      });

      // Perform comparison based on type
      let comparisonResult: any;

      switch (comparisonType) {
        case 'content':
          comparisonResult = {
            similarity: 0.75,
            differences: [
              'Document 1 has more technical content',
              'Document 2 focuses more on business aspects',
            ],
            commonThemes: ['data processing', 'automation'],
          };
          break;

        case 'sentiment':
          const sentiment1 = analysis1.analysisResults.find(
            r => r.type === 'sentiment_analysis'
          )?.data;
          const sentiment2 = analysis2.analysisResults.find(
            r => r.type === 'sentiment_analysis'
          )?.data;

          comparisonResult = {
            document1Sentiment: sentiment1?.overall?.sentiment || 'neutral',
            document2Sentiment: sentiment2?.overall?.sentiment || 'neutral',
            sentimentDifference: Math.abs(
              (sentiment1?.overall?.score || 0) - (sentiment2?.overall?.score || 0)
            ),
            analysis: 'Both documents have similar sentiment patterns',
          };
          break;

        default:
          comparisonResult = {
            message: `Comparison type ${comparisonType} not implemented yet`,
          };
      }

      return {
        documentId1,
        documentId2,
        comparisonType,
        result: comparisonResult,
        confidence: 0.82,
        processingTime: 3000,
        timestamp: new Date(),
      };
    },
    onSuccess: () => {
      toast.success('Document comparison completed!');
    },
    onError: (error: Error) => {
      toast.error(`Document comparison failed: ${error.message}`);
    },
  });

  return {
    // Queries
    getAnalysisResults,

    // Mutations
    analyzeDocument,
    batchAnalyze,
    customAnalysis,
    compareDocuments,

    // Loading states
    isAnalyzing: analyzeDocument.isPending,
    isBatchAnalyzing: batchAnalyze.isPending,
    isCustomAnalyzing: customAnalysis.isPending,
    isComparing: compareDocuments.isPending,

    // Error states
    analysisError:
      analyzeDocument.error || batchAnalyze.error || customAnalysis.error || compareDocuments.error,

    // Data
    lastAnalysis: analyzeDocument.data,
    lastBatchAnalysis: batchAnalyze.data,
    lastCustomAnalysis: customAnalysis.data,
    lastComparison: compareDocuments.data,
  };
};
