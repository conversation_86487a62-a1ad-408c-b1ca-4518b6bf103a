import React from 'react';
import { QueryClient, QueryClientProvider, QueryCache, MutationCache } from '@tanstack/react-query';
import { persistQueryClient } from '@tanstack/react-query-persist-client';
import { toast } from 'react-hot-toast';
import Store from 'electron-store';

// Create Electron Store for cache persistence
const createCachePersister = () => {
  const store = new Store({
    name: 'react-query-cache',
    defaults: {},
    serialize: JSON.stringify,
    deserialize: JSON.parse,
  });

  return {
    persistClient: async (client: any) => {
      try {
        const serializedState = JSON.stringify({
          clientState: client
            .getQueryCache()
            .getAll()
            .map((query: any) => ({
              queryKey: query.queryKey,
              queryHash: query.queryHash,
              state: query.state,
            })),
          timestamp: Date.now(),
        });
        store.set('queryCache', serializedState);
      } catch (error) {
        console.error('Failed to persist query cache:', error);
      }
    },
    restoreClient: async () => {
      try {
        const cached = store.get('queryCache') as string;
        if (cached) {
          const parsed = JSON.parse(cached);
          // Only restore if cache is less than 24 hours old
          if (Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {
            return parsed.clientState;
          }
        }
      } catch (error) {
        console.error('Failed to restore query cache:', error);
      }
      return undefined;
    },
    removeClient: async () => {
      try {
        store.delete('queryCache');
      } catch (error) {
        console.error('Failed to remove query cache:', error);
      }
    },
  };
};

// Create a query client with optimized configuration
const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Stale time: 5 minutes
        staleTime: 5 * 60 * 1000,
        // Cache time: 10 minutes
        gcTime: 10 * 60 * 1000,
        // Retry failed requests 3 times with exponential backoff
        retry: (failureCount, error: any) => {
          // Don't retry on 4xx errors (client errors)
          if (error?.status >= 400 && error?.status < 500) {
            return false;
          }
          return failureCount < 3;
        },
        retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
        // Refetch on window focus
        refetchOnWindowFocus: true,
        // Refetch on reconnect
        refetchOnReconnect: true,
        // Background refetch interval: 5 minutes
        refetchInterval: 5 * 60 * 1000,
        // Enable query deduplication
        structuralSharing: true,
        // Network mode for offline support
        networkMode: 'offlineFirst',
      },
      mutations: {
        // Retry failed mutations once
        retry: 1,
        retryDelay: 1000,
      },
    },
    queryCache: new QueryCache({
      onError: (error: any, query) => {
        // Log query errors
        console.error('Query error:', error, query);

        if (window.electronAPI?.logToMain) {
          window.electronAPI.logToMain('error', 'React Query Error', {
            error: {
              message: error?.message,
              status: error?.status,
              stack: error?.stack,
            },
            queryKey: query.queryKey,
            timestamp: new Date().toISOString(),
          });
        }

        // Show user-friendly error message
        if (error?.status >= 500) {
          toast.error('Server error. Please try again later.');
        } else if (error?.status === 404) {
          toast.error('Resource not found.');
        } else if (error?.status >= 400) {
          toast.error(error?.message || 'Request failed.');
        } else {
          toast.error('Network error. Please check your connection.');
        }
      },
      onSuccess: (data, query) => {
        // Log successful queries in development
        if (process.env.NODE_ENV === 'development') {
          console.log('Query success:', query.queryKey, data);
        }
      },
    }),
    mutationCache: new MutationCache({
      onError: (error: any, _variables, _context, mutation) => {
        // Log mutation errors
        console.error('Mutation error:', error, mutation);

        if (window.electronAPI?.logToMain) {
          window.electronAPI.logToMain('error', 'React Query Mutation Error', {
            error: {
              message: error?.message,
              status: error?.status,
              stack: error?.stack,
            },
            mutationKey: mutation.options.mutationKey,
            _variables,
            timestamp: new Date().toISOString(),
          });
        }

        // Show user-friendly error message
        toast.error(error?.message || 'Operation failed. Please try again.');
      },
      onSuccess: (data, _variables, _context, mutation) => {
        // Log successful mutations in development
        if (process.env.NODE_ENV === 'development') {
          console.log('Mutation success:', mutation.options.mutationKey, data);
        }

        // Show success message for certain mutations
        if (mutation.options.mutationKey?.[0] === 'document-process') {
          toast.success('Document processed successfully!');
        } else if (mutation.options.mutationKey?.[0] === 'form-fill') {
          toast.success('Form filled successfully!');
        }
      },
    }),
  });
};

// Create the query client instance
const queryClient = createQueryClient();

// Set up cache persistence
const cachePersister = createCachePersister();

// Initialize persistence
persistQueryClient({
  queryClient,
  persister: cachePersister,
  maxAge: 24 * 60 * 60 * 1000, // 24 hours
  hydrateOptions: {
    // Only hydrate successful queries
    defaultOptions: {
      queries: {
        // staleTime: 5 * 60 * 1000, // 5 minutes
      },
    },
  },
  dehydrateOptions: {
    // Only persist successful queries
    shouldDehydrateQuery: query => {
      return query.state.status === 'success';
    },
  },
});

/**
 * React Query provider component with optimized configuration
 */
export const QueryProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
};

/**
 * Hook to get the query client instance
 */
export const useQueryClient = () => {
  return queryClient;
};

/**
 * Query keys factory for consistent key management
 */
export const queryKeys = {
  // Document queries
  documents: ['documents'] as const,
  document: (id: string) => ['documents', id] as const,
  documentProcessing: (id: string) => ['documents', id, 'processing'] as const,
  documentExtraction: (id: string) => ['documents', id, 'extraction'] as const,

  // AI queries
  aiCompletion: (prompt: string) => ['ai', 'completion', prompt] as const,
  aiEmbedding: (text: string) => ['ai', 'embedding', text] as const,
  aiAnalysis: (documentId: string) => ['ai', 'analysis', documentId] as const,

  // Knowledge base queries
  knowledge: ['knowledge'] as const,
  knowledgeSearch: (query: string) => ['knowledge', 'search', query] as const,
  knowledgeGraph: ['knowledge', 'graph'] as const,

  // Timeline queries
  timeline: ['timeline'] as const,
  timelineEntry: (id: string) => ['timeline', id] as const,
  timelineBranches: ['timeline', 'branches'] as const,

  // Settings queries
  settings: ['settings'] as const,
  settingsAI: ['settings', 'ai'] as const,
  settingsTheme: ['settings', 'theme'] as const,

  // Form queries
  forms: ['forms'] as const,
  form: (id: string) => ['forms', id] as const,
  formFields: (id: string) => ['forms', id, 'fields'] as const,
  formTemplates: ['forms', 'templates'] as const,
} as const;

/**
 * Mutation keys factory for consistent key management
 */
export const mutationKeys = {
  // Document mutations
  documentUpload: ['document-upload'] as const,
  documentProcess: ['document-process'] as const,
  documentDelete: ['document-delete'] as const,

  // AI mutations
  aiCompletion: ['ai-completion'] as const,
  aiAnalysis: ['ai-analysis'] as const,

  // Form mutations
  formFill: ['form-fill'] as const,
  formSave: ['form-save'] as const,
  formTemplateCreate: ['form-template-create'] as const,

  // Knowledge mutations
  knowledgeAdd: ['knowledge-add'] as const,
  knowledgeUpdate: ['knowledge-update'] as const,
  knowledgeDelete: ['knowledge-delete'] as const,

  // Timeline mutations
  timelineCheckpoint: ['timeline-checkpoint'] as const,
  timelineRestore: ['timeline-restore'] as const,

  // Settings mutations
  settingsUpdate: ['settings-update'] as const,
} as const;

/**
 * Utility function to invalidate related queries
 */
export const invalidateQueries = {
  documents: () => queryClient.invalidateQueries({ queryKey: queryKeys.documents }),
  document: (id: string) => queryClient.invalidateQueries({ queryKey: queryKeys.document(id) }),
  knowledge: () => queryClient.invalidateQueries({ queryKey: queryKeys.knowledge }),
  timeline: () => queryClient.invalidateQueries({ queryKey: queryKeys.timeline }),
  settings: () => queryClient.invalidateQueries({ queryKey: queryKeys.settings }),
  all: () => queryClient.invalidateQueries(),
};

export default QueryProvider;
