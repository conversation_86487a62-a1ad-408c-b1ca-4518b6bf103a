// UI and application state type definitions

export interface Tab {
  id: string;
  title: string;
  filePath: string;
  documentId?: string;
  type: TabType;
  isActive: boolean;
  isDirty: boolean;
  isPinned: boolean;
  isLoading: boolean;
  groupId?: string;
  icon?: string;
  tooltip?: string;
  metadata: TabMetadata;
  createdAt: Date;
  lastAccessedAt: Date;
}

export enum TabType {
  DOCUMENT = 'document',
  FORM = 'form',
  KNOWLEDGE_BASE = 'knowledge_base',
  TIMELINE = 'timeline',
  SETTINGS = 'settings',
  CHAT = 'chat',
  DIFF_VIEWER = 'diff_viewer',
  TEMPLATE_EDITOR = 'template_editor',
}

export interface TabMetadata {
  documentType?: string;
  fileSize?: number;
  lastModified?: Date;
  processingStatus?: ProcessingStatus;
  validationStatus?: ValidationStatus;
  aiAnalysisStatus?: AnalysisStatus;
  customProperties: Record<string, unknown>;
}

export enum ProcessingStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum ValidationStatus {
  NOT_VALIDATED = 'not_validated',
  VALIDATING = 'validating',
  VALID = 'valid',
  INVALID = 'invalid',
  PARTIALLY_VALID = 'partially_valid',
}

export enum AnalysisStatus {
  NOT_ANALYZED = 'not_analyzed',
  ANALYZING = 'analyzing',
  ANALYZED = 'analyzed',
  ANALYSIS_FAILED = 'analysis_failed',
}

export interface TabGroup {
  id: string;
  name: string;
  color: string;
  tabs: string[];
  isCollapsed: boolean;
  position: number;
  metadata: TabGroupMetadata;
}

export interface TabGroupMetadata {
  description?: string;
  tags: string[];
  createdAt: Date;
  lastModified: Date;
  customProperties: Record<string, unknown>;
}

export interface TabManager {
  tabs: Tab[];
  groups: TabGroup[];
  activeTabId: string | null;
  maxTabs: number;
  tabHistory: string[];
  recentlyClosedTabs: ClosedTab[];
  settings: TabManagerSettings;

  // Tab operations
  openTab(tab: Omit<Tab, 'id' | 'createdAt' | 'lastAccessedAt'>): Promise<string>;
  closeTab(tabId: string): Promise<void>;
  activateTab(tabId: string): Promise<void>;
  pinTab(tabId: string): Promise<void>;
  unpinTab(tabId: string): Promise<void>;
  duplicateTab(tabId: string): Promise<string>;
  moveTab(tabId: string, newIndex: number): Promise<void>;

  // Group operations
  createGroup(name: string, tabIds: string[]): Promise<string>;
  addToGroup(tabId: string, groupId: string): Promise<void>;
  removeFromGroup(tabId: string): Promise<void>;
  deleteGroup(groupId: string): Promise<void>;

  // Session management
  saveSession(): Promise<string>;
  restoreSession(sessionId: string): Promise<void>;
  getRecentSessions(): Promise<TabSession[]>;
}

export interface ClosedTab {
  tab: Tab;
  closedAt: Date;
  position: number;
  groupId?: string;
}

export interface TabManagerSettings {
  autoSave: boolean;
  maxRecentlyClosedTabs: number;
  tabCloseConfirmation: boolean;
  groupTabsByType: boolean;
  showTabIcons: boolean;
  showTabTooltips: boolean;
  tabWidth: TabWidth;
}

export enum TabWidth {
  NARROW = 'narrow',
  NORMAL = 'normal',
  WIDE = 'wide',
  AUTO = 'auto',
}

export interface TabSession {
  id: string;
  name: string;
  tabs: Tab[];
  groups: TabGroup[];
  activeTabId: string | null;
  createdAt: Date;
  lastModified: Date;
  metadata: SessionMetadata;
}

export interface SessionMetadata {
  description?: string;
  tags: string[];
  isAutoSaved: boolean;
  totalTabs: number;
  totalGroups: number;
}

export interface ApplicationConfig {
  version: string;
  environment: Environment;
  features: FeatureFlags;
  performance: PerformanceConfig;
  security: SecurityConfig;
  ai: AIConfig;
  ui: UIConfig;
  storage: StorageConfig;
  logging: LoggingConfig;
  updates: UpdateConfig;
}

export enum Environment {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
  TEST = 'test',
}

export interface FeatureFlags {
  aiProcessing: boolean;
  ocrEngine: boolean;
  knowledgeBase: boolean;
  timelineFeatures: boolean;
  collaborativeEditing: boolean;
  experimentalFeatures: boolean;
  betaFeatures: boolean;
}

export interface PerformanceConfig {
  maxConcurrentOperations: number;
  cacheSize: number;
  memoryLimit: number;
  diskSpaceThreshold: number;
  networkTimeout: number;
  retryAttempts: number;
  batchSize: number;
}

export interface SecurityConfig {
  encryptionEnabled: boolean;
  auditLogging: boolean;
  sessionTimeout: number;
  maxLoginAttempts: number;
  passwordPolicy: PasswordPolicy;
  dataRetention: DataRetentionPolicy;
}

export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  maxAge: number;
}

export interface DataRetentionPolicy {
  logRetentionDays: number;
  backupRetentionDays: number;
  sessionRetentionDays: number;
  tempFileRetentionHours: number;
}

export interface AIConfig {
  providers: AIProviderConfig[];
  defaultProvider: string;
  fallbackProvider?: string;
  rateLimiting: RateLimitConfig;
  caching: CacheConfig;
  monitoring: MonitoringConfig;
}

export interface AIProviderConfig {
  id: string;
  name: string;
  type: string;
  endpoint: string;
  apiKey: string;
  models: string[];
  isActive: boolean;
  priority: number;
}

export interface RateLimitConfig {
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  burstLimit: number;
}

export interface CacheConfig {
  enabled: boolean;
  ttl: number;
  maxSize: number;
  strategy: CacheStrategy;
}

export enum CacheStrategy {
  LRU = 'lru',
  LFU = 'lfu',
  FIFO = 'fifo',
  TTL = 'ttl',
}

export interface MonitoringConfig {
  enabled: boolean;
  alertThresholds: AlertThresholds;
  reportingEndpoint?: string;
}

export interface AlertThresholds {
  errorRate: number;
  responseTime: number;
  memoryUsage: number;
  diskUsage: number;
}

export interface LayoutConfig {
  defaultLayout: LayoutState;
  allowCustomization: boolean;
  persistLayout: boolean;
  responsiveBreakpoints: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
}

export interface UIConfig {
  theme: ThemeConfig;
  layout: LayoutConfig;
  accessibility: AccessibilityConfig;
  animations: AnimationConfig;
  notifications: NotificationConfig;
}

export interface StorageConfig {
  databasePath: string;
  backupPath: string;
  tempPath: string;
  maxFileSize: number;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
}

export interface LoggingConfig {
  level: LogLevel;
  maxFileSize: number;
  maxFiles: number;
  format: LogFormat;
  destinations: LogDestination[];
}

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal',
}

export enum LogFormat {
  JSON = 'json',
  TEXT = 'text',
  STRUCTURED = 'structured',
}

export enum LogDestination {
  FILE = 'file',
  CONSOLE = 'console',
  REMOTE = 'remote',
}

export interface UpdateConfig {
  autoCheck: boolean;
  autoDownload: boolean;
  autoInstall: boolean;
  channel: UpdateChannel;
  checkInterval: number;
}

export enum UpdateChannel {
  STABLE = 'stable',
  BETA = 'beta',
  ALPHA = 'alpha',
  NIGHTLY = 'nightly',
}

export interface UserPreferences {
  general: GeneralPreferences;
  editor: EditorPreferences;
  ai: AIPreferences;
  appearance: AppearancePreferences;
  keyboard: KeyboardPreferences;
  privacy: PrivacyPreferences;
  advanced: AdvancedPreferences;
}

export interface GeneralPreferences {
  language: string;
  region: string;
  timezone: string;
  autoSave: boolean;
  autoSaveInterval: number;
  confirmOnExit: boolean;
  restoreSession: boolean;
  checkForUpdates: boolean;
}

export interface EditorPreferences {
  fontSize: number;
  fontFamily: string;
  lineHeight: number;
  tabSize: number;
  wordWrap: boolean;
  showLineNumbers: boolean;
  showWhitespace: boolean;
  highlightCurrentLine: boolean;
  autoIndent: boolean;
}

export interface AIPreferences {
  preferredProvider: string;
  preferredModel: string;
  temperature: number;
  maxTokens: number;
  responseStyle: ResponseStyle;
  autoAnalyze: boolean;
  showConfidence: boolean;
  enableSuggestions: boolean;
}

export enum ResponseStyle {
  CONCISE = 'concise',
  DETAILED = 'detailed',
  TECHNICAL = 'technical',
  CASUAL = 'casual',
  PROFESSIONAL = 'professional',
}

export interface AppearancePreferences {
  theme: string;
  colorScheme: ColorScheme;
  accentColor: string;
  density: UIDensity;
  animations: boolean;
  transparency: boolean;
  customCSS?: string;
}

export enum ColorScheme {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto',
  HIGH_CONTRAST = 'high_contrast',
}

export enum UIDensity {
  COMPACT = 'compact',
  NORMAL = 'normal',
  COMFORTABLE = 'comfortable',
}

export interface KeyboardPreferences {
  shortcuts: KeyboardShortcut[];
  customShortcuts: CustomShortcut[];
  enableVimMode: boolean;
  enableEmacsMode: boolean;
}

export interface PrivacyPreferences {
  crashReportingEnabled: boolean;
  dataCollection: DataCollectionSettings;
}

export interface DataCollectionSettings {
  collectPerformanceData: boolean;
  collectErrorReports: boolean;
}

export interface AdvancedPreferences {
  developerMode: boolean;
  debugLogging: boolean;
  experimentalFeatures: boolean;
  performanceMode: PerformanceMode;
  memoryLimit: number;
  networkProxy?: ProxySettings;
}

export enum PerformanceMode {
  BALANCED = 'balanced',
  PERFORMANCE = 'performance',
  BATTERY_SAVER = 'battery_saver',
  CUSTOM = 'custom',
}

export interface ProxySettings {
  enabled: boolean;
  host: string;
  port: number;
  username?: string;
  password?: string;
  bypassList: string[];
}

export interface SessionData {
  id: string;
  userId?: string;
  startTime: Date;
  lastActivity: Date;
  isActive: boolean;
  tabs: string[];
  activeTab?: string;
  preferences: UserPreferences;
  state: SessionState;
  metadata: SessionDataMetadata;
}

export interface SessionState {
  windowState: WindowState;
  sidebarState: SidebarState;
  panelStates: PanelState[];
  recentFiles: string[];
  searchHistory: string[];
  clipboardHistory: ClipboardEntry[];
}

export interface WindowState {
  bounds: WindowBounds;
  isMaximized: boolean;
  isMinimized: boolean;
  isFullscreen: boolean;
  isAlwaysOnTop: boolean;
}

export interface WindowBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface SidebarState {
  isVisible: boolean;
  width: number;
  position: SidebarPosition;
  activePanel: string;
  expandedItems: string[];
  pinnedItems: string[];
}

export enum SidebarPosition {
  LEFT = 'left',
  RIGHT = 'right',
}

export interface PanelState {
  id: string;
  isVisible: boolean;
  size: number;
  position: PanelPosition;
  isCollapsed: boolean;
  isDocked: boolean;
}

export enum PanelPosition {
  TOP = 'top',
  BOTTOM = 'bottom',
  LEFT = 'left',
  RIGHT = 'right',
  FLOATING = 'floating',
}

export interface ClipboardEntry {
  id: string;
  content: string;
  type: ClipboardType;
  timestamp: Date;
  source?: string;
}

export enum ClipboardType {
  TEXT = 'text',
  HTML = 'html',
  IMAGE = 'image',
  FILE = 'file',
  RICH_TEXT = 'rich_text',
}

export interface SessionDataMetadata {
  version: string;
  platform: string;
  userAgent: string;
  totalDocuments: number;
  totalProcessingTime: number;
  lastBackup?: Date;
}

export interface NotificationOptions {
  id?: string;
  title: string;
  message: string;
  type: NotificationType;
  duration?: number;
  persistent?: boolean;
  actions?: NotificationAction[];
  icon?: string;
  sound?: boolean;
  priority: NotificationPriority;
  metadata?: Record<string, unknown>;
}

export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  PROGRESS = 'progress',
}

export interface NotificationAction {
  id: string;
  label: string;
  action: () => void;
  style?: ActionStyle;
}

export enum ActionStyle {
  PRIMARY = 'primary',
  SECONDARY = 'secondary',
  DANGER = 'danger',
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface ToastMessage {
  id: string;
  title?: string;
  message: string;
  type: ToastType;
  duration: number;
  position: ToastPosition;
  dismissible: boolean;
  actions?: ToastAction[];
  progress?: number;
  timestamp: Date;
}

export enum ToastType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  LOADING = 'loading',
}

export enum ToastPosition {
  TOP_LEFT = 'top-left',
  TOP_CENTER = 'top-center',
  TOP_RIGHT = 'top-right',
  BOTTOM_LEFT = 'bottom-left',
  BOTTOM_CENTER = 'bottom-center',
  BOTTOM_RIGHT = 'bottom-right',
}

export interface ToastAction {
  label: string;
  action: () => void;
  style?: ActionStyle;
}

export interface ModalState {
  id: string;
  type: ModalType;
  title: string;
  content: ModalContent;
  size: ModalSize;
  position: ModalPosition;
  isVisible: boolean;
  isClosable: boolean;
  backdrop: BackdropType;
  animation: AnimationType;
  zIndex: number;
  metadata?: Record<string, unknown>;
}

export enum ModalType {
  DIALOG = 'dialog',
  ALERT = 'alert',
  CONFIRM = 'confirm',
  FORM = 'form',
  CUSTOM = 'custom',
}

export interface ModalContent {
  component?: string;
  props?: Record<string, unknown>;
  html?: string;
  text?: string;
}

export enum ModalSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  EXTRA_LARGE = 'extra_large',
  FULL_SCREEN = 'full_screen',
}

export enum ModalPosition {
  CENTER = 'center',
  TOP = 'top',
  BOTTOM = 'bottom',
  LEFT = 'left',
  RIGHT = 'right',
}

export enum BackdropType {
  STATIC = 'static',
  CLICKABLE = 'clickable',
  NONE = 'none',
}

export enum AnimationType {
  FADE = 'fade',
  SLIDE = 'slide',
  ZOOM = 'zoom',
  NONE = 'none',
}

export interface KeyboardShortcut {
  id: string;
  name: string;
  description: string;
  keys: string[];
  action: string;
  context?: ShortcutContext;
  enabled: boolean;
  global: boolean;
  preventDefault: boolean;
}

export enum ShortcutContext {
  GLOBAL = 'global',
  EDITOR = 'editor',
  FORM = 'form',
  TIMELINE = 'timeline',
  KNOWLEDGE_BASE = 'knowledge_base',
}

export interface CustomShortcut {
  id: string;
  name: string;
  keys: string[];
  command: string;
  args?: Record<string, unknown>;
  when?: string;
}

export interface MenuAction {
  id: string;
  label: string;
  icon?: string;
  shortcut?: string;
  action: MenuActionHandler;
  enabled: boolean;
  visible: boolean;
  separator?: boolean;
  submenu?: MenuAction[];
  metadata?: Record<string, unknown>;
}

export type MenuActionHandler = (context?: MenuActionContext) => void | Promise<void>;

export interface MenuActionContext {
  target?: EventTarget | null;
  position?: Position;
  data?: Record<string, unknown>;
}

export interface Position {
  x: number;
  y: number;
}

export interface ContextMenuOptions {
  items: MenuAction[];
  position: Position;
  target?: EventTarget | null;
  theme?: string;
  animation?: boolean;
  closeOnClick?: boolean;
  closeOnScroll?: boolean;
  zIndex?: number;
}

export interface ThemeConfig {
  id: string;
  name: string;
  type: ThemeType;
  colors: ColorPalette;
  typography: Typography;
  spacing: Spacing;
  shadows: Shadows;
  borders: Borders;
  animations: AnimationConfig;
  customProperties?: Record<string, string>;
}

export enum ThemeType {
  LIGHT = 'light',
  DARK = 'dark',
  HIGH_CONTRAST = 'high_contrast',
  CUSTOM = 'custom',
}

export interface ColorPalette {
  primary: ColorScale;
  secondary: ColorScale;
  accent: ColorScale;
  neutral: ColorScale;
  success: ColorScale;
  warning: ColorScale;
  error: ColorScale;
  info: ColorScale;
  background: BackgroundColors;
  text: TextColors;
  border: BorderColors;
}

export interface ColorScale {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
}

export interface BackgroundColors {
  primary: string;
  secondary: string;
  tertiary: string;
  overlay: string;
  modal: string;
  tooltip: string;
}

export interface TextColors {
  primary: string;
  secondary: string;
  tertiary: string;
  inverse: string;
  disabled: string;
  link: string;
}

export interface BorderColors {
  primary: string;
  secondary: string;
  focus: string;
  error: string;
  success: string;
}

export interface Typography {
  fontFamily: FontFamily;
  fontSize: FontSizeScale;
  fontWeight: FontWeightScale;
  lineHeight: LineHeightScale;
  letterSpacing: LetterSpacingScale;
}

export interface FontFamily {
  sans: string[];
  serif: string[];
  mono: string[];
  display: string[];
}

export interface FontSizeScale {
  xs: string;
  sm: string;
  base: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  '4xl': string;
  '5xl': string;
  '6xl': string;
}

export interface FontWeightScale {
  thin: number;
  light: number;
  normal: number;
  medium: number;
  semibold: number;
  bold: number;
  extrabold: number;
  black: number;
}

export interface LineHeightScale {
  none: number;
  tight: number;
  snug: number;
  normal: number;
  relaxed: number;
  loose: number;
}

export interface LetterSpacingScale {
  tighter: string;
  tight: string;
  normal: string;
  wide: string;
  wider: string;
  widest: string;
}

export interface Spacing {
  scale: SpacingScale;
  component: ComponentSpacing;
}

export interface SpacingScale {
  0: string;
  1: string;
  2: string;
  3: string;
  4: string;
  5: string;
  6: string;
  8: string;
  10: string;
  12: string;
  16: string;
  20: string;
  24: string;
  32: string;
  40: string;
  48: string;
  56: string;
  64: string;
}

export interface ComponentSpacing {
  button: ComponentSpacingValues;
  input: ComponentSpacingValues;
  card: ComponentSpacingValues;
  modal: ComponentSpacingValues;
}

export interface ComponentSpacingValues {
  padding: string;
  margin: string;
  gap: string;
}

export interface Shadows {
  sm: string;
  base: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  inner: string;
  none: string;
}

export interface Borders {
  width: BorderWidthScale;
  radius: BorderRadiusScale;
  style: BorderStyleScale;
}

export interface BorderWidthScale {
  0: string;
  1: string;
  2: string;
  4: string;
  8: string;
}

export interface BorderRadiusScale {
  none: string;
  sm: string;
  base: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  '3xl': string;
  full: string;
}

export interface BorderStyleScale {
  solid: string;
  dashed: string;
  dotted: string;
  double: string;
  none: string;
}

export interface AnimationConfig {
  duration: AnimationDurationScale;
  easing: AnimationEasingScale;
  enabled: boolean;
  reducedMotion: boolean;
}

export interface AnimationDurationScale {
  fast: string;
  normal: string;
  slow: string;
  slower: string;
}

export interface AnimationEasingScale {
  linear: string;
  ease: string;
  easeIn: string;
  easeOut: string;
  easeInOut: string;
  bounce: string;
}

export interface LayoutState {
  windowBounds: WindowBounds;
  panels: PanelLayoutState[];
  sidebar: SidebarLayoutState;
  toolbar: ToolbarLayoutState;
  statusBar: StatusBarLayoutState;
  splitters: SplitterState[];
}

export interface PanelLayoutState {
  id: string;
  type: PanelType;
  position: PanelPosition;
  size: PanelSize;
  isVisible: boolean;
  isCollapsed: boolean;
  isDocked: boolean;
  zIndex: number;
}

export enum PanelType {
  FILE_EXPLORER = 'file_explorer',
  KNOWLEDGE_BASE = 'knowledge_base',
  TIMELINE = 'timeline',
  AI_CHAT = 'ai_chat',
  PROPERTIES = 'properties',
  OUTLINE = 'outline',
  SEARCH = 'search',
  PROBLEMS = 'problems',
}

export interface PanelSize {
  width: number;
  height: number;
  minWidth: number;
  minHeight: number;
  maxWidth?: number;
  maxHeight?: number;
}

export interface SidebarLayoutState {
  position: SidebarPosition;
  width: number;
  isVisible: boolean;
  isCollapsed: boolean;
  activePanel: string;
  panels: string[];
}

export interface ToolbarLayoutState {
  isVisible: boolean;
  height: number;
  items: ToolbarItem[];
  customization: ToolbarCustomization;
}

export interface ToolbarItem {
  id: string;
  type: ToolbarItemType;
  label?: string;
  icon?: string;
  action?: string;
  enabled: boolean;
  visible: boolean;
  position: number;
}

export enum ToolbarItemType {
  BUTTON = 'button',
  DROPDOWN = 'dropdown',
  SEPARATOR = 'separator',
  SPACER = 'spacer',
  CUSTOM = 'custom',
}

export interface ToolbarCustomization {
  allowReorder: boolean;
  allowHide: boolean;
  allowCustomItems: boolean;
  showLabels: boolean;
  iconSize: IconSize;
}

export enum IconSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
}

export interface StatusBarLayoutState {
  isVisible: boolean;
  height: number;
  items: StatusBarItem[];
}

export interface StatusBarItem {
  id: string;
  type: StatusBarItemType;
  content: string;
  tooltip?: string;
  action?: string;
  position: StatusBarPosition;
  priority: number;
  visible: boolean;
}

export enum StatusBarItemType {
  TEXT = 'text',
  PROGRESS = 'progress',
  BUTTON = 'button',
  INDICATOR = 'indicator',
}

export enum StatusBarPosition {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right',
}

export interface SplitterState {
  id: string;
  orientation: SplitterOrientation;
  position: number;
  size: number;
  isResizing: boolean;
  minPosition: number;
  maxPosition: number;
}

export enum SplitterOrientation {
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical',
}

export interface PanelConfiguration {
  id: string;
  type: PanelType;
  title: string;
  icon?: string;
  component: string;
  defaultSize: PanelSize;
  defaultPosition: PanelPosition;
  resizable: boolean;
  closable: boolean;
  collapsible: boolean;
  dockable: boolean;
  settings: PanelSettings;
}

export interface PanelSettings {
  showHeader: boolean;
  showToolbar: boolean;
  allowCustomization: boolean;
  persistState: boolean;
  refreshInterval?: number;
  maxItems?: number;
  sortOptions?: SortOption[];
  filterOptions?: FilterOption[];
}

export interface SortOption {
  id: string;
  label: string;
  field: string;
  direction: SortDirection;
}

export interface FilterOption {
  id: string;
  label: string;
  field: string;
  type: FilterType;
  options?: FilterOptionValue[];
}

export enum FilterType {
  TEXT = 'text',
  SELECT = 'select',
  MULTI_SELECT = 'multi_select',
  DATE_RANGE = 'date_range',
  NUMBER_RANGE = 'number_range',
  BOOLEAN = 'boolean',
}

export interface FilterOptionValue {
  value: unknown;
  label: string;
}

export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

export interface AccessibilityConfig {
  enabled: boolean;
  highContrast: boolean;
  largeText: boolean;
  reducedMotion: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
  focusIndicators: boolean;
  announcements: boolean;
  skipLinks: boolean;
}

export interface NotificationConfig {
  enabled: boolean;
  position: ToastPosition;
  duration: number;
  maxVisible: number;
  showProgress: boolean;
  playSound: boolean;
  groupSimilar: boolean;
  persistentTypes: NotificationType[];
}

// Additional types for store compatibility
export interface ToastState {
  id: string;
  type: ToastType;
  title: string;
  message: string;
  duration?: number;
  isVisible: boolean;
  isHidden: boolean;
  createdAt: Date;
  actions?: ToastAction[];
}

export interface NotificationState {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  priority: NotificationPriority;
  isRead: boolean;
  createdAt: Date;
  expiresAt?: Date;
  actions?: NotificationAction[];
  metadata?: Record<string, any>;
}
