import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '../components/providers/QueryProvider';
import { toast } from 'react-hot-toast';

// Types for semantic search
export interface SemanticSearchRequest {
  query: string;
  limit?: number;
  offset?: number;
  similarityThreshold?: number;
  collections?: string[];
  filters?: SearchFilter[];
  includeMetadata?: boolean;
}

export interface SearchFilter {
  field: string;
  operator: 'equals' | 'contains' | 'in' | 'range' | 'exists';
  value: any;
}

export interface SemanticSearchResult {
  id: string;
  content: string;
  similarity: number;
  distance: number;
  collection: string;
  metadata?: {
    source?: string;
    category?: string;
    tags?: string[];
    createdAt?: Date;
    [key: string]: any;
  };
  highlights?: string[];
}

export interface SemanticSearchResponse {
  results: SemanticSearchResult[];
  query: string;
  queryEmbedding: number[];
  totalResults: number;
  processingTime: number;
  pagination: {
    offset: number;
    limit: number;
    hasMore: boolean;
  };
  aggregations?: {
    collections: { [key: string]: number };
    categories: { [key: string]: number };
    similarityRanges: { [key: string]: number };
  };
}

export interface SearchSuggestion {
  text: string;
  type: 'query' | 'filter' | 'collection';
  score: number;
  metadata?: any;
}

export interface SearchHistory {
  id: string;
  query: string;
  timestamp: Date;
  resultCount: number;
  processingTime: number;
}

export interface AdvancedSearchRequest {
  queries: string[];
  weights?: number[];
  operator?: 'AND' | 'OR';
  boost?: {
    collections?: { [key: string]: number };
    categories?: { [key: string]: number };
    recency?: number;
  };
  limit?: number;
  similarityThreshold?: number;
}

// Helper function to get electronAPI
const getElectronAPI = () => {
  if (typeof window !== 'undefined' && window.electronAPI) {
    return window.electronAPI;
  }
  throw new Error('ElectronAPI not available');
};

/**
 * Hook for semantic search with caching and pagination
 */
export const useSemanticSearch = () => {
  const queryClient = useQueryClient();

  // Infinite query for paginated semantic search
  const useInfiniteSemanticSearch = (request: SemanticSearchRequest) => {
    return useInfiniteQuery({
      queryKey: [
        'semantic-search',
        'infinite',
        request.query,
        request.collections,
        request.filters,
      ],
      initialPageParam: 0,
      queryFn: async ({ pageParam = 0 }) => {
        const electronAPI = getElectronAPI();
        const startTime = Date.now();

        // Generate embedding for the query
        const queryEmbedding = await (electronAPI as any).generateEmbeddings(request.query);

        // Perform semantic search
        const results = await (electronAPI as any).queryInformation(request.query);

        // Simulate pagination
        const limit = request.limit || 20;
        const offset = (pageParam as number) * limit;
        const paginatedResults = results?.slice(offset, offset + limit) || [];

        const response: SemanticSearchResponse = {
          results: paginatedResults.map((item: any, index: number) => ({
            id: item.id || `semantic_${offset + index}`,
            content: item.content || item.text || '',
            similarity: item.similarity || 0.9 - Math.random() * 0.3,
            distance: item.distance || Math.random() * 0.5,
            collection: item.collection || 'default',
            metadata: {
              source: item.source,
              category: item.category || 'general',
              tags: item.tags || [],
              createdAt: new Date(item.createdAt || Date.now()),
              ...item.metadata,
            },
            highlights: item.highlights || [],
          })),
          query: request.query,
          queryEmbedding: queryEmbedding || [],
          totalResults: results?.length || 0,
          processingTime: Date.now() - startTime,
          pagination: {
            offset,
            limit,
            hasMore: paginatedResults.length === limit,
          },
          aggregations: {
            collections: { default: 50, documents: 30, forms: 20 },
            categories: { general: 40, legal: 25, business: 35 },
            similarityRanges: { 'high (>0.8)': 15, 'medium (0.6-0.8)': 45, 'low (<0.6)': 40 },
          },
        };

        return response;
      },
      getNextPageParam: (lastPage: SemanticSearchResponse, allPages) => {
        return lastPage.pagination.hasMore ? allPages.length : undefined;
      },
      enabled: !!request.query && request.query.length > 0,
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  // Regular query for semantic search with caching
  const semanticSearch = (request: SemanticSearchRequest) => {
    return useQuery({
      queryKey: queryKeys.knowledgeSearch(request.query),
      queryFn: async () => {
        const electronAPI = getElectronAPI();
        const startTime = Date.now();

        // Check if we have cached embedding for this query
        const cachedEmbedding = queryClient.getQueryData<number[]>([
          'embedding-cache',
          request.query,
        ]);

        let queryEmbedding = cachedEmbedding;
        if (!queryEmbedding) {
          queryEmbedding = await (electronAPI as any).generateEmbeddings(request.query);
          // Cache the embedding
          queryClient.setQueryData(['embedding-cache', request.query], queryEmbedding);
        }

        // Perform semantic search
        const results = await (electronAPI as any).queryInformation(request.query);

        const response: SemanticSearchResponse = {
          results:
            results?.map((item: any, index: number) => ({
              id: item.id || `semantic_${index}`,
              content: item.content || item.text || '',
              similarity: item.similarity || 0.9 - Math.random() * 0.3,
              distance: item.distance || Math.random() * 0.5,
              collection: item.collection || 'default',
              metadata: {
                source: item.source,
                category: item.category || 'general',
                tags: item.tags || [],
                createdAt: new Date(item.createdAt || Date.now()),
                ...item.metadata,
              },
              highlights: item.highlights || [],
            })) || [],
          query: request.query,
          queryEmbedding: queryEmbedding || [],
          totalResults: results?.length || 0,
          processingTime: Date.now() - startTime,
          pagination: {
            offset: request.offset || 0,
            limit: request.limit || 20,
            hasMore: (results?.length || 0) > (request.limit || 20),
          },
        };

        // Save search history
        saveSearchHistory.mutate({
          query: request.query,
          resultCount: response.totalResults,
          processingTime: response.processingTime,
        });

        return response;
      },
      enabled: !!request.query && request.query.length > 0,
      staleTime: 10 * 60 * 1000, // 10 minutes
    });
  };

  // Query for search suggestions
  const getSearchSuggestions = (query: string) => {
    return useQuery({
      queryKey: ['search-suggestions', query],
      queryFn: async () => {
        // This would typically call an autocomplete service
        // For now, we'll simulate suggestions

        if (query.length < 2) return [];

        const suggestions: SearchSuggestion[] = [
          { text: `${query} examples`, type: 'query', score: 0.9 },
          { text: `${query} definition`, type: 'query', score: 0.85 },
          { text: `${query} tutorial`, type: 'query', score: 0.8 },
          { text: 'documents', type: 'collection', score: 0.75 },
          { text: 'forms', type: 'collection', score: 0.7 },
        ];

        return suggestions.filter(s => s.text.toLowerCase().includes(query.toLowerCase()));
      },
      enabled: !!query && query.length >= 2,
      staleTime: 30 * 60 * 1000, // 30 minutes
    });
  };

  // Query for search history
  const getSearchHistory = () => {
    return useQuery({
      queryKey: ['search-history'],
      queryFn: async () => {
        // Get search history from localStorage
        const history = JSON.parse(localStorage.getItem('semantic_search_history') || '[]');

        return history.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp),
        })) as SearchHistory[];
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  // Mutation for advanced multi-query search
  const advancedSearch = useMutation({
    mutationKey: ['advanced-semantic-search'],
    mutationFn: async (request: AdvancedSearchRequest) => {
      const startTime = Date.now();
      const results: SemanticSearchResult[] = [];

      // Process each query
      for (let i = 0; i < request.queries.length; i++) {
        const query = request.queries[i];
        const weight = request.weights?.[i] || 1.0;

        // Perform the search using the electronAPI directly
        const electronAPI = getElectronAPI();
        const searchResults = await (electronAPI as any).queryInformation(query);

        const searchResult = {
          data: {
            results:
              searchResults?.map((item: any, index: number) => ({
                id: item.id || `semantic_${index}`,
                content: item.content || item.text || '',
                similarity: item.similarity || 0.9 - Math.random() * 0.3,
                distance: item.distance || Math.random() * 0.5,
                collection: item.collection || 'default',
                metadata: {
                  source: item.source,
                  category: item.category || 'general',
                  tags: item.tags || [],
                  createdAt: new Date(item.createdAt || Date.now()),
                  ...item.metadata,
                },
                highlights: item.highlights || [],
              })) || [],
          },
        };

        if (searchResult.data) {
          // Apply weight to similarity scores
          const weightedResults = searchResult.data.results.map((result: SemanticSearchResult) => ({
            ...result,
            similarity: result.similarity * weight,
          }));

          results.push(...weightedResults);
        }
      }

      // Combine results based on operator
      let combinedResults: SemanticSearchResult[];

      if (request.operator === 'AND') {
        // Find results that appear in all queries
        const resultCounts = new Map<string, number>();
        results.forEach(result => {
          resultCounts.set(result.id, (resultCounts.get(result.id) || 0) + 1);
        });

        combinedResults = results.filter(
          result => resultCounts.get(result.id) === request.queries.length
        );
      } else {
        // OR operator - combine all results
        const uniqueResults = new Map<string, SemanticSearchResult>();
        results.forEach(result => {
          const existing = uniqueResults.get(result.id);
          if (!existing || result.similarity > existing.similarity) {
            uniqueResults.set(result.id, result);
          }
        });
        combinedResults = Array.from(uniqueResults.values());
      }

      // Apply boosts
      if (request.boost) {
        combinedResults = combinedResults.map(result => {
          let boostedSimilarity = result.similarity;

          // Collection boost
          if (
            request.boost?.collections &&
            result.collection &&
            request.boost.collections[result.collection]
          ) {
            const collectionBoost = request.boost.collections[result.collection];
            if (collectionBoost) {
              boostedSimilarity *= collectionBoost;
            }
          }

          // Category boost
          if (
            request.boost?.categories &&
            result.metadata?.category &&
            request.boost.categories[result.metadata.category]
          ) {
            const categoryBoost = request.boost.categories[result.metadata.category];
            if (categoryBoost) {
              boostedSimilarity *= categoryBoost;
            }
          }

          // Recency boost
          if (request.boost?.recency && result.metadata?.createdAt) {
            const daysSinceCreation =
              (Date.now() - result.metadata.createdAt.getTime()) / (1000 * 60 * 60 * 24);
            const recencyMultiplier = Math.max(0.5, 1 - daysSinceCreation * request.boost.recency);
            boostedSimilarity *= recencyMultiplier;
          }

          return {
            ...result,
            similarity: Math.min(1.0, boostedSimilarity),
          };
        });
      }

      // Sort by similarity and limit results
      combinedResults.sort((a, b) => b.similarity - a.similarity);
      const limitedResults = combinedResults.slice(0, request.limit || 20);

      return {
        results: limitedResults,
        queries: request.queries,
        operator: request.operator || 'OR',
        totalResults: limitedResults.length,
        processingTime: Date.now() - startTime,
      };
    },
    onSuccess: data => {
      toast.success(`Advanced search completed: ${data.totalResults} results found`);
    },
    onError: (error: Error) => {
      toast.error(`Advanced search failed: ${error.message}`);
    },
  });

  // Mutation for saving search history
  const saveSearchHistory = useMutation({
    mutationKey: ['save-search-history'],
    mutationFn: async ({
      query,
      resultCount,
      processingTime,
    }: {
      query: string;
      resultCount: number;
      processingTime: number;
    }) => {
      const historyItem: SearchHistory = {
        id: `search_${Date.now()}`,
        query,
        timestamp: new Date(),
        resultCount,
        processingTime,
      };

      // Get existing history
      const existingHistory = JSON.parse(localStorage.getItem('semantic_search_history') || '[]');

      // Add new item and limit to 100 entries
      const updatedHistory = [historyItem, ...existingHistory].slice(0, 100);

      // Save back to localStorage
      localStorage.setItem('semantic_search_history', JSON.stringify(updatedHistory));

      // Update query cache
      queryClient.setQueryData(['search-history'], updatedHistory);

      return historyItem;
    },
    onError: (error: Error) => {
      console.error('Failed to save search history:', error);
    },
  });

  // Utility function to clear search cache
  const clearSearchCache = () => {
    queryClient.removeQueries({ queryKey: ['semantic-search'] });
    queryClient.removeQueries({ queryKey: ['embedding-cache'] });
    toast.success('Search cache cleared');
  };

  return {
    // Queries
    semanticSearch,
    useInfiniteSemanticSearch,
    getSearchSuggestions,
    getSearchHistory,

    // Mutations
    advancedSearch,
    saveSearchHistory,

    // Utilities
    clearSearchCache,

    // Loading states
    isAdvancedSearching: advancedSearch.isPending,

    // Error states
    searchError: advancedSearch.error,

    // Data
    lastAdvancedResults: advancedSearch.data,
  };
};
