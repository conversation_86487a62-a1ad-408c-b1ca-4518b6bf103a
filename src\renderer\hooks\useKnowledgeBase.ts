import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { queryKeys, mutationKeys, invalidateQueries } from '../components/providers/QueryProvider';
import { toast } from 'react-hot-toast';

// Types for knowledge base operations
export interface KnowledgeItem {
  id: string;
  content: string;
  category: string;
  tags: string[];
  metadata: {
    source?: string;
    sourceDocument?: string;
    confidence?: number;
    createdAt: Date;
    updatedAt: Date;
    [key: string]: any;
  };
  embedding?: number[];
}

export interface KnowledgeStoreRequest {
  content: string;
  category: string;
  tags?: string[];
  metadata?: any;
  sourceDocument?: string;
  generateEmbedding?: boolean;
}

export interface KnowledgeUpdateRequest {
  id: string;
  content?: string;
  category?: string;
  tags?: string[];
  metadata?: any;
  regenerateEmbedding?: boolean;
}

export interface KnowledgeQueryRequest {
  query?: string;
  category?: string;
  tags?: string[];
  limit?: number;
  offset?: number;
  sortBy?: 'relevance' | 'date' | 'category';
  sortOrder?: 'asc' | 'desc';
}

export interface KnowledgeQueryResponse {
  items: KnowledgeItem[];
  totalItems: number;
  categories: string[];
  tags: string[];
  pagination: {
    offset: number;
    limit: number;
    hasMore: boolean;
  };
}

export interface KnowledgeStats {
  totalItems: number;
  totalCategories: number;
  totalTags: number;
  recentItems: number;
  storageSize: number;
  lastUpdated: Date;
}

export interface BulkKnowledgeOperation {
  items: KnowledgeStoreRequest[];
  options?: {
    skipDuplicates?: boolean;
    batchSize?: number;
    generateEmbeddings?: boolean;
  };
}

export interface BulkOperationResult {
  successCount: number;
  failureCount: number;
  totalItems: number;
  errors: string[];
  processingTime: number;
}

// Helper function to get electronAPI
const getElectronAPI = () => {
  if (typeof window !== 'undefined' && window.electronAPI) {
    return window.electronAPI;
  }
  throw new Error('ElectronAPI not available');
};

/**
 * Hook for knowledge base operations
 */
export const useKnowledgeBase = () => {
  const queryClient = useQueryClient();

  // Query for getting knowledge items
  const getKnowledgeItems = (request: KnowledgeQueryRequest = {}) => {
    return useQuery({
      queryKey: queryKeys.knowledge,
      queryFn: async () => {
        const electronAPI = getElectronAPI();

        // Call the knowledge base service through IPC
        const results = await (electronAPI as any).queryInformation(request.query || '');

        const response: KnowledgeQueryResponse = {
          items:
            results?.map((item: any, index: number) => ({
              id: item.id || `knowledge_${index}`,
              content: item.content || item.text || '',
              category: item.category || 'general',
              tags: item.tags || [],
              metadata: {
                source: item.source,
                sourceDocument: item.sourceDocument,
                confidence: item.confidence || 0.8,
                createdAt: new Date(item.createdAt || Date.now()),
                updatedAt: new Date(item.updatedAt || Date.now()),
                ...item.metadata,
              },
              embedding: item.embedding,
            })) || [],
          totalItems: results?.length || 0,
          categories: ['general', 'documents', 'forms', 'analysis'],
          tags: ['important', 'processed', 'verified'],
          pagination: {
            offset: request.offset || 0,
            limit: request.limit || 20,
            hasMore: (results?.length || 0) >= (request.limit || 20),
          },
        };

        return response;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchInterval: 10 * 60 * 1000, // Background refetch every 10 minutes
    });
  };

  // Query for getting knowledge base statistics
  const getKnowledgeStats = () => {
    return useQuery({
      queryKey: ['knowledge', 'stats'],
      queryFn: async () => {
        // This would call an IPC method to get knowledge base stats
        // For now, we'll simulate the response
        const stats: KnowledgeStats = {
          totalItems: 1250,
          totalCategories: 8,
          totalTags: 45,
          recentItems: 23,
          storageSize: 15.7, // MB
          lastUpdated: new Date(),
        };

        return stats;
      },
      staleTime: 30 * 60 * 1000, // 30 minutes
      refetchInterval: 15 * 60 * 1000, // Background refetch every 15 minutes
    });
  };

  // Mutation for storing knowledge
  const storeKnowledge = useMutation({
    mutationKey: mutationKeys.knowledgeAdd,
    mutationFn: async (request: KnowledgeStoreRequest) => {
      const electronAPI = getElectronAPI();

      // Prepare the knowledge item
      const knowledgeItem = {
        id: `knowledge_${Date.now()}`,
        content: request.content,
        category: request.category,
        tags: request.tags || [],
        metadata: {
          source: 'manual',
          sourceDocument: request.sourceDocument,
          confidence: 1.0,
          createdAt: new Date(),
          updatedAt: new Date(),
          ...request.metadata,
        },
      };

      // Generate embedding if requested
      if (request.generateEmbedding) {
        const embedding = await (electronAPI as any).generateEmbeddings(request.content);
        (knowledgeItem as any).embedding = embedding;
      }

      // Store the knowledge item
      await (electronAPI as any).storeInformation(knowledgeItem);

      return knowledgeItem;
    },
    onSuccess: _data => {
      // Invalidate knowledge queries to refresh the data
      invalidateQueries.knowledge();

      toast.success('Knowledge item stored successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to store knowledge: ${error.message}`);
    },
  });

  // Mutation for updating knowledge
  const updateKnowledge = useMutation({
    mutationKey: mutationKeys.knowledgeUpdate,
    mutationFn: async (request: KnowledgeUpdateRequest) => {
      // This would call an IPC method to update knowledge
      // For now, we'll simulate the update

      const updatedItem: KnowledgeItem = {
        id: request.id,
        content: request.content || '',
        category: request.category || 'general',
        tags: request.tags || [],
        metadata: {
          updatedAt: new Date(),
          ...request.metadata,
        },
      };

      // Regenerate embedding if requested
      if (request.regenerateEmbedding && request.content) {
        const electronAPI = getElectronAPI();
        const embedding = await (electronAPI as any).generateEmbeddings(request.content);
        updatedItem.embedding = embedding;
      }

      return updatedItem;
    },
    onSuccess: data => {
      // Update the cache with the new data
      queryClient.setQueryData(
        queryKeys.knowledge,
        (oldData: KnowledgeQueryResponse | undefined) => {
          if (!oldData) return oldData;

          const updatedItems = oldData.items.map(item => (item.id === data.id ? data : item));

          return {
            ...oldData,
            items: updatedItems,
          };
        }
      );

      toast.success('Knowledge item updated successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to update knowledge: ${error.message}`);
    },
  });

  // Mutation for deleting knowledge
  const deleteKnowledge = useMutation({
    mutationKey: mutationKeys.knowledgeDelete,
    mutationFn: async (id: string) => {
      // This would call an IPC method to delete knowledge
      // For now, we'll simulate the deletion

      return { id, deleted: true };
    },
    onSuccess: data => {
      // Remove the item from cache
      queryClient.setQueryData(
        queryKeys.knowledge,
        (oldData: KnowledgeQueryResponse | undefined) => {
          if (!oldData) return oldData;

          const filteredItems = oldData.items.filter(item => item.id !== data.id);

          return {
            ...oldData,
            items: filteredItems,
            totalItems: oldData.totalItems - 1,
          };
        }
      );

      toast.success('Knowledge item deleted successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete knowledge: ${error.message}`);
    },
  });

  // Mutation for bulk operations
  const bulkStoreKnowledge = useMutation({
    mutationKey: ['knowledge-bulk-store'],
    mutationFn: async (operation: BulkKnowledgeOperation) => {
      const startTime = Date.now();
      const batchSize = operation.options?.batchSize || 10;
      let successCount = 0;
      let failureCount = 0;
      const errors: string[] = [];

      // Process in batches
      for (let i = 0; i < operation.items.length; i += batchSize) {
        const batch = operation.items.slice(i, i + batchSize);

        for (const item of batch) {
          try {
            await storeKnowledge.mutateAsync(item);
            successCount++;
          } catch (error) {
            failureCount++;
            errors.push(
              `Item ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`
            );
          }
        }

        // Small delay between batches
        if (i + batchSize < operation.items.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      const result: BulkOperationResult = {
        successCount,
        failureCount,
        totalItems: operation.items.length,
        errors,
        processingTime: Date.now() - startTime,
      };

      return result;
    },
    onSuccess: data => {
      invalidateQueries.knowledge();

      if (data.failureCount === 0) {
        toast.success(`All ${data.totalItems} knowledge items stored successfully!`);
      } else {
        toast.success(
          `${data.successCount}/${data.totalItems} knowledge items stored successfully`
        );
      }
    },
    onError: (error: Error) => {
      toast.error(`Bulk knowledge storage failed: ${error.message}`);
    },
  });

  // Mutation for knowledge cleanup
  const cleanupKnowledge = useMutation({
    mutationKey: ['knowledge-cleanup'],
    mutationFn: async ({
      removeOlderThan,
      removeByCategory,
      removeDuplicates,
    }: {
      removeOlderThan?: Date;
      removeByCategory?: string[];
      removeDuplicates?: boolean;
    }) => {
      // This would call an IPC method to cleanup knowledge
      // For now, we'll simulate the cleanup

      let removedCount = 0;

      if (removeOlderThan) {
        removedCount += 15; // Simulate removing old items
      }

      if (removeByCategory?.length) {
        removedCount += removeByCategory.length * 5; // Simulate removing by category
      }

      if (removeDuplicates) {
        removedCount += 8; // Simulate removing duplicates
      }

      return {
        removedCount,
        operations: {
          removeOlderThan: !!removeOlderThan,
          removeByCategory: removeByCategory?.length || 0,
          removeDuplicates: !!removeDuplicates,
        },
        processingTime: 2000,
      };
    },
    onSuccess: data => {
      invalidateQueries.knowledge();
      toast.success(`Knowledge cleanup completed! ${data.removedCount} items removed.`);
    },
    onError: (error: Error) => {
      toast.error(`Knowledge cleanup failed: ${error.message}`);
    },
  });

  return {
    // Queries
    getKnowledgeItems,
    getKnowledgeStats,

    // Mutations
    storeKnowledge,
    updateKnowledge,
    deleteKnowledge,
    bulkStoreKnowledge,
    cleanupKnowledge,

    // Loading states
    isStoring: storeKnowledge.isPending,
    isUpdating: updateKnowledge.isPending,
    isDeleting: deleteKnowledge.isPending,
    isBulkStoring: bulkStoreKnowledge.isPending,
    isCleaning: cleanupKnowledge.isPending,

    // Error states
    knowledgeError: storeKnowledge.error || updateKnowledge.error || deleteKnowledge.error,

    // Data
    lastStored: storeKnowledge.data,
    lastBulkResult: bulkStoreKnowledge.data,
    lastCleanupResult: cleanupKnowledge.data,
  };
};
