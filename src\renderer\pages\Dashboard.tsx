import React from 'react';
import { useAppNavigation } from '../router/RouterProvider';
import { AppLayout } from '../components/layout';

const Dashboard: React.FC = () => {
  const { navigate: _navigate } = useAppNavigation();

  return (
    <AppLayout title='Dashboard'>
      <div className='h-full flex items-center justify-center bg-base-100'>
        <div className='text-center max-w-md'>
          <div className='mb-8'>
            <div className='w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4'>
              <svg
                className='w-8 h-8 text-primary'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                />
              </svg>
            </div>
            <h1 className='text-2xl font-bold text-base-content mb-2'>AI Document Processor</h1>
            <p className='text-base-content/70 mb-6'>
              Enterprise-grade AI-powered document processing and intelligent paperwork management
              system
            </p>
          </div>

          <div className='space-y-3'>
            <button
              className='btn btn-primary btn-block'
              onClick={() => {
                console.log('Open Document clicked');
                if (window.electronAPI?.logToMain) {
                  window.electronAPI.logToMain('info', 'User clicked Open Document button', {
                    component: 'Dashboard',
                    action: 'openDocument',
                  });
                }
              }}
            >
              Open Document
            </button>
            <button
              className='btn btn-outline btn-block'
              onClick={() => {
                console.log('Create New Project clicked');
                if (window.electronAPI?.logToMain) {
                  window.electronAPI.logToMain('info', 'User clicked Create New Project button', {
                    component: 'Dashboard',
                    action: 'createProject',
                  });
                }
              }}
            >
              Create New Project
            </button>
          </div>

          <div className='mt-8 text-xs text-base-content/50'>
            Version 1.0.0 • Built with Electron & React
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default Dashboard;
