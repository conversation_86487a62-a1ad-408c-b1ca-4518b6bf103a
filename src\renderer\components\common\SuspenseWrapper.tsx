import React, { Suspense } from 'react';
import { LoadingSpinner } from './LoadingSpinner';
import { ErrorBoundary } from './ErrorBoundary';

interface SuspenseWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  errorFallback?: React.ReactNode;
  loadingText?: string;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

/**
 * Enhanced Suspense wrapper with error boundary and custom loading states
 */
export const SuspenseWrapper: React.FC<SuspenseWrapperProps> = ({
  children,
  fallback,
  errorFallback,
  loadingText = 'Loading component...',
  onError,
}) => {
  const defaultFallback = (
    <div className='flex items-center justify-center p-8'>
      <LoadingSpinner text={loadingText} size='lg' />
    </div>
  );

  const errorBoundaryProps: {
    fallback?: React.ReactNode;
    onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  } = {
    fallback: errorFallback,
  };

  if (onError) {
    errorBoundaryProps.onError = onError;
  }

  return (
    <ErrorBoundary {...errorBoundaryProps}>
      <Suspense fallback={fallback || defaultFallback}>{children}</Suspense>
    </ErrorBoundary>
  );
};

/**
 * Page-level suspense wrapper with full-screen loading
 */
export const PageSuspenseWrapper: React.FC<{
  children: React.ReactNode;
  loadingText?: string;
}> = ({ children, loadingText = 'Loading page...' }) => {
  return (
    <SuspenseWrapper
      fallback={
        <div className='min-h-screen flex items-center justify-center bg-base-100'>
          <LoadingSpinner text={loadingText} size='xl' />
        </div>
      }
    >
      {children}
    </SuspenseWrapper>
  );
};

/**
 * Component-level suspense wrapper with inline loading
 */
export const ComponentSuspenseWrapper: React.FC<{
  children: React.ReactNode;
  loadingText?: string;
  className?: string;
}> = ({ children, loadingText = 'Loading...', className = '' }) => {
  return (
    <SuspenseWrapper
      fallback={
        <div className={`flex items-center justify-center p-4 ${className}`}>
          <LoadingSpinner text={loadingText} size='md' />
        </div>
      }
    >
      {children}
    </SuspenseWrapper>
  );
};

export default SuspenseWrapper;
