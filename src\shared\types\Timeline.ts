// Timeline and version control type definitions

export interface TimelineEntry {
  id: string;
  type: TimelineEntryType;
  action: ActionType;
  description: string;
  beforeState?: StateSnapshot;
  afterState?: StateSnapshot;
  affectedDocuments: string[];
  userId?: string;
  sessionId: string;
  parentId?: string;
  children?: string[];
  metadata: TimelineMetadata;
  createdAt: Date;
  processingTime: number;
}

export enum TimelineEntryType {
  DOCUMENT_OPERATION = 'document_operation',
  AI_OPERATION = 'ai_operation',
  USER_ACTION = 'user_action',
  SYSTEM_EVENT = 'system_event',
  CHECKPOINT = 'checkpoint',
  BRANCH_OPERATION = 'branch_operation',
  MERGE_OPERATION = 'merge_operation',
}

export enum ActionType {
  // Document operations
  DOCUMENT_CREATED = 'document_created',
  DOCUMENT_UPDATED = 'document_updated',
  DOCUMENT_DELETED = 'document_deleted',
  DOCUMENT_PROCESSED = 'document_processed',

  // Form operations
  FORM_FILLED = 'form_filled',
  FORM_VALIDATED = 'form_validated',
  FIELD_MAPPED = 'field_mapped',

  // AI operations
  AI_ANALYSIS = 'ai_analysis',
  KNOWLEDGE_EXTRACTED = 'knowledge_extracted',
  TEMPLATE_APPLIED = 'template_applied',

  // Version control
  CHECKPOINT_CREATED = 'checkpoint_created',
  CHECKPOINT_RESTORED = 'checkpoint_restored',
  BRANCH_CREATED = 'branch_created',
  BRANCH_SWITCHED = 'branch_switched',
  BRANCHES_MERGED = 'branches_merged',

  // User actions
  ANNOTATION_ADDED = 'annotation_added',
  SIGNATURE_ADDED = 'signature_added',
  COMMENT_ADDED = 'comment_added',

  // System events
  SESSION_STARTED = 'session_started',
  SESSION_ENDED = 'session_ended',
  ERROR_OCCURRED = 'error_occurred',
  BACKUP_CREATED = 'backup_created',
}

export interface TimelineMetadata {
  confidence?: number;
  cost?: number;
  tokensUsed?: number;
  errorCode?: string;
  warningCount: number;
  tags: string[];
  category: string;
  priority: TimelinePriority;
  isReversible: boolean;
  dependencies: string[];
}

export enum TimelinePriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export type CheckpointId = string;

export interface ApplicationState {
  id: string;
  version: string;
  timestamp: Date;
  documents: DocumentState[];
  knowledgeBase: KnowledgeBaseState;
  userInterface: UIState;
  aiContext: AIContextState;
  configuration: ConfigurationState;
  metadata: StateMetadata;
}

export interface DocumentState {
  documentId: string;
  content: unknown;
  metadata: Record<string, unknown>;
  annotations: AnnotationState[];
  formData: FormDataState;
  processingResults: ProcessingResultState[];
  version: number;
  checksum: string;
}

export interface AnnotationState {
  id: string;
  type: string;
  content: string;
  coordinates: CoordinateState;
  author: string;
  timestamp: Date;
}

export interface CoordinateState {
  x: number;
  y: number;
  width: number;
  height: number;
  pageNumber: number;
}

export interface FormDataState {
  fields: Record<string, unknown>;
  validationResults: ValidationResultState[];
  completionPercentage: number;
  lastModified: Date;
}

export interface ValidationResultState {
  fieldId: string;
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ProcessingResultState {
  id: string;
  type: string;
  result: unknown;
  confidence: number;
  timestamp: Date;
}

export interface KnowledgeBaseState {
  entries: KnowledgeEntryState[];
  relationships: RelationshipState[];
  statistics: KnowledgeStatistics;
  lastUpdated: Date;
}

export interface KnowledgeEntryState {
  id: string;
  category: string;
  content: string;
  confidence: number;
  sourceDocument: string;
}

export interface RelationshipState {
  id: string;
  sourceId: string;
  targetId: string;
  type: string;
  strength: number;
}

export interface KnowledgeStatistics {
  totalEntries: number;
  totalRelationships: number;
  averageConfidence: number;
  lastIndexed: Date;
}

export interface UIState {
  activeTab: string;
  openTabs: TabState[];
  sidebarState: SidebarState;
  preferences: UserPreferencesState;
  layout: LayoutState;
}

export interface TabState {
  id: string;
  documentId: string;
  title: string;
  isActive: boolean;
  isDirty: boolean;
  scrollPosition: number;
  zoomLevel: number;
}

export interface SidebarState {
  isVisible: boolean;
  width: number;
  activePanel: string;
  expandedItems: string[];
}

export interface UserPreferencesState {
  theme: string;
  language: string;
  fontSize: number;
  autoSave: boolean;
  notifications: boolean;
}

export interface LayoutState {
  windowSize: WindowSize;
  panelSizes: Record<string, number>;
  isMaximized: boolean;
  isFullscreen: boolean;
}

export interface WindowSize {
  width: number;
  height: number;
}

export interface AIContextState {
  activeSession: string;
  conversationHistory: ConversationEntry[];
  knowledgeContext: string[];
  preferences: AIPreferencesState;
}

export interface ConversationEntry {
  id: string;
  role: string;
  content: string;
  timestamp: Date;
}

export interface AIPreferencesState {
  preferredModel: string;
  temperature: number;
  maxTokens: number;
  responseStyle: string;
}

export interface ConfigurationState {
  aiProviders: AIProviderState[];
  databaseConfig: DatabaseConfigState;
  securitySettings: SecuritySettingsState;
  performanceSettings: PerformanceSettingsState;
}

export interface AIProviderState {
  id: string;
  name: string;
  isActive: boolean;
  endpoint: string;
  models: string[];
}

export interface DatabaseConfigState {
  path: string;
  backupEnabled: boolean;
  backupInterval: number;
  maxBackups: number;
}

export interface SecuritySettingsState {
  encryptionEnabled: boolean;
  auditLogging: boolean;
  sessionTimeout: number;
  maxLoginAttempts: number;
}

export interface PerformanceSettingsState {
  cacheSize: number;
  maxConcurrentOperations: number;
  memoryLimit: number;
  diskSpaceThreshold: number;
}

export interface StateMetadata {
  size: number;
  compressionRatio: number;
  checksum: string;
  dependencies: string[];
  tags: string[];
}

export interface DiffResult {
  id: string;
  beforeCheckpoint: CheckpointId;
  afterCheckpoint: CheckpointId;
  changes: ChangeSet[];
  statistics: DiffStatistics;
  createdAt: Date;
}

export interface ChangeSet {
  type: ChangeType;
  path: string;
  beforeValue: unknown;
  afterValue: unknown;
  confidence: number;
  description: string;
}

export enum ChangeType {
  ADDED = 'added',
  MODIFIED = 'modified',
  DELETED = 'deleted',
  MOVED = 'moved',
  RENAMED = 'renamed',
}

export interface DiffStatistics {
  totalChanges: number;
  addedItems: number;
  modifiedItems: number;
  deletedItems: number;
  movedItems: number;
  renamedItems: number;
}

export interface VisualDiff {
  id: string;
  type: VisualDiffType;
  beforeImage?: ImageData;
  afterImage?: ImageData;
  diffImage?: ImageData;
  textDiff?: TextDiff;
  structuralDiff?: StructuralDiff;
  metadata: VisualDiffMetadata;
}

export enum VisualDiffType {
  TEXT = 'text',
  IMAGE = 'image',
  PDF = 'pdf',
  STRUCTURAL = 'structural',
  HYBRID = 'hybrid',
}

export interface ImageData {
  width: number;
  height: number;
  format: string;
  data: ArrayBuffer;
}

export interface TextDiff {
  lines: TextDiffLine[];
  statistics: TextDiffStatistics;
}

export interface TextDiffLine {
  lineNumber: number;
  type: TextDiffLineType;
  content: string;
  beforeLineNumber?: number;
  afterLineNumber?: number;
}

export enum TextDiffLineType {
  UNCHANGED = 'unchanged',
  ADDED = 'added',
  DELETED = 'deleted',
  MODIFIED = 'modified',
}

export interface TextDiffStatistics {
  totalLines: number;
  addedLines: number;
  deletedLines: number;
  modifiedLines: number;
  unchangedLines: number;
}

export interface StructuralDiff {
  elements: StructuralDiffElement[];
  statistics: StructuralDiffStatistics;
}

export interface StructuralDiffElement {
  id: string;
  type: string;
  changeType: ChangeType;
  path: string;
  beforeProperties?: Record<string, unknown>;
  afterProperties?: Record<string, unknown>;
}

export interface StructuralDiffStatistics {
  totalElements: number;
  addedElements: number;
  modifiedElements: number;
  deletedElements: number;
  movedElements: number;
}

export interface VisualDiffMetadata {
  algorithm: string;
  threshold: number;
  processingTime: number;
  accuracy: number;
  createdAt: Date;
}

export interface MergeResult {
  id: string;
  sourceBranch: BranchId;
  targetBranch: BranchId;
  resultCheckpoint: CheckpointId;
  conflicts: MergeConflict[];
  resolutions: ConflictResolution[];
  statistics: MergeStatistics;
  success: boolean;
  createdAt: Date;
}

export interface MergeConflict {
  id: string;
  type: ConflictType;
  path: string;
  sourceValue: unknown;
  targetValue: unknown;
  baseValue?: unknown;
  description: string;
  severity: ConflictSeverity;
}

export enum ConflictType {
  CONTENT = 'content',
  STRUCTURE = 'structure',
  METADATA = 'metadata',
  DEPENDENCY = 'dependency',
  PERMISSION = 'permission',
}

export enum ConflictSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface ConflictResolution {
  conflictId: string;
  strategy: ResolutionStrategy;
  resolvedValue: unknown;
  reasoning: string;
  confidence: number;
  isManual: boolean;
  resolvedBy?: string;
  resolvedAt: Date;
}

export enum ResolutionStrategy {
  TAKE_SOURCE = 'take_source',
  TAKE_TARGET = 'take_target',
  MERGE_BOTH = 'merge_both',
  CUSTOM = 'custom',
  SKIP = 'skip',
}

export interface MergeStatistics {
  totalConflicts: number;
  resolvedConflicts: number;
  unresolvedConflicts: number;
  automaticResolutions: number;
  manualResolutions: number;
  processingTime: number;
}

export type BranchId = string;

export interface DocumentVersion {
  id: string;
  documentId: string;
  version: number;
  checkpointId: CheckpointId;
  branchId: BranchId;
  content: unknown;
  metadata: VersionMetadata;
  parentVersion?: string;
  childVersions: string[];
  createdAt: Date;
}

export interface VersionMetadata {
  author: string;
  description: string;
  tags: string[];
  size: number;
  checksum: string;
  contentType: string;
  encoding?: string;
  compression?: CompressionInfo;
}

export interface CompressionInfo {
  algorithm: string;
  originalSize: number;
  compressedSize: number;
  ratio: number;
}

export interface UndoRedoState {
  undoStack: CheckpointId[];
  redoStack: CheckpointId[];
  currentPosition: number;
  maxStackSize: number;
  canUndo: boolean;
  canRedo: boolean;
}

export interface TimelineFilter {
  types?: TimelineEntryType[];
  actions?: ActionType[];
  dateRange?: DateRange;
  users?: string[];
  documents?: string[];
  branches?: BranchId[];
  tags?: string[];
  priority?: TimelinePriority[];
  textSearch?: string;
}

export interface DateRange {
  start: Date;
  end: Date;
}

export interface TimelineQuery {
  filter?: TimelineFilter;
  sort?: TimelineSort;
  pagination?: TimelinePagination;
  includeMetadata?: boolean;
  includeState?: boolean;
}

export interface TimelineSort {
  field: TimelineSortField;
  direction: SortDirection;
}

export enum TimelineSortField {
  CREATED_AT = 'created_at',
  TYPE = 'type',
  ACTION = 'action',
  USER = 'user',
  PRIORITY = 'priority',
  PROCESSING_TIME = 'processing_time',
}

export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

export interface TimelinePagination {
  offset: number;
  limit: number;
  total?: number;
}

export interface CompressionOptions {
  algorithm: CompressionAlgorithm;
  level: number;
  dictionary?: ArrayBuffer;
  chunkSize?: number;
  parallel?: boolean;
}

export enum CompressionAlgorithm {
  GZIP = 'gzip',
  BROTLI = 'brotli',
  LZ4 = 'lz4',
  ZSTD = 'zstd',
  NONE = 'none',
}

export interface StateSnapshot {
  id: string;
  checkpointId: CheckpointId;
  state: ApplicationState;
  compression: CompressionOptions;
  metadata: SnapshotMetadata;
  createdAt: Date;
}

export interface SnapshotMetadata {
  size: number;
  compressedSize: number;
  compressionRatio: number;
  checksum: string;
  dependencies: string[];
  tags: string[];
  description: string;
}

// Branch management interfaces
export interface Branch {
  id: BranchId;
  name: string;
  description: string;
  parentBranch?: BranchId;
  headCheckpoint: CheckpointId;
  createdAt: Date;
  createdBy: string;
  isActive: boolean;
  isProtected: boolean;
  metadata: BranchMetadata;
}

export interface BranchMetadata {
  tags: string[];
  category: string;
  purpose: string;
  collaborators: string[];
  permissions: BranchPermissions;
}

export interface BranchPermissions {
  canRead: string[];
  canWrite: string[];
  canMerge: string[];
  canDelete: string[];
}

// Timeline visualization interfaces
export interface TimelineVisualization {
  entries: VisualTimelineEntry[];
  branches: VisualBranch[];
  connections: VisualConnection[];
  layout: TimelineLayout;
}

export interface VisualTimelineEntry {
  entry: TimelineEntry;
  position: TimelinePosition;
  style: EntryStyle;
  connections: string[];
}

export interface TimelinePosition {
  x: number;
  y: number;
  lane: number;
  level: number;
}

export interface EntryStyle {
  color: string;
  size: number;
  shape: EntryShape;
  icon?: string;
}

export enum EntryShape {
  CIRCLE = 'circle',
  SQUARE = 'square',
  DIAMOND = 'diamond',
  TRIANGLE = 'triangle',
}

export interface VisualBranch {
  branch: Branch;
  path: BranchPath;
  style: BranchStyle;
}

export interface BranchPath {
  points: TimelinePosition[];
  startPoint: TimelinePosition;
  endPoint: TimelinePosition;
}

export interface BranchStyle {
  color: string;
  width: number;
  style: LineStyle;
}

export enum LineStyle {
  SOLID = 'solid',
  DASHED = 'dashed',
  DOTTED = 'dotted',
}

export interface VisualConnection {
  id: string;
  source: string;
  target: string;
  type: ConnectionType;
  style: ConnectionStyle;
}

export enum ConnectionType {
  PARENT_CHILD = 'parent_child',
  BRANCH = 'branch',
  MERGE = 'merge',
  DEPENDENCY = 'dependency',
}

export interface ConnectionStyle {
  color: string;
  width: number;
  style: LineStyle;
  arrow: boolean;
}

export interface TimelineLayout {
  width: number;
  height: number;
  lanes: number;
  spacing: LayoutSpacing;
  orientation: LayoutOrientation;
}

export interface LayoutSpacing {
  horizontal: number;
  vertical: number;
  lane: number;
}

export enum LayoutOrientation {
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical',
}

// Timeline view modes and display options
export enum TimelineViewMode {
  LIST = 'list',
  GRAPH = 'graph',
  TREE = 'tree',
  TIMELINE = 'timeline',
  COMPACT = 'compact',
}

export interface TimelineDisplayOptions {
  viewMode: TimelineViewMode;
  showMetadata: boolean;
  showThumbnails: boolean;
  groupByDate: boolean;
  groupByType: boolean;
  showBranches: boolean;
  showConnections: boolean;
  compactMode: boolean;
  colorScheme: string;
  fontSize: number;
  itemSpacing: number;
}
