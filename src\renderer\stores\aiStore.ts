import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { v4 as uuid } from 'uuid';
import {
  AIProvider,
  AIModel,
  AIRequest,
  AIResponse,
  ChatSession,
  ChatMessage,
  UserPreferences,
  AIContext,
  RequestPriority,
  UsageStats,
  HealthStatus,
  AISession,
  ProviderHealthStatus,
} from '../../shared/types/AI';

export interface AIState {
  // Provider and model management
  providers: AIProvider[];
  activeProviderId: string | null;
  selectedModel: string | null;
  availableModels: AIModel[];

  // Chat and conversation management
  activeSessions: ChatSession[];
  currentSessionId: string | null;
  conversationHistory: Map<string, ChatMessage[]>;

  // Request and response management
  pendingRequests: Map<string, AIRequest>;
  responseCache: Map<string, AIResponse>;
  requestQueue: AIRequest[];

  // AI context and preferences
  currentContext: AIContext | null;
  userPreferences: UserPreferences;

  // Processing state
  isProcessing: boolean;
  isGenerating: boolean;
  isAnalyzing: boolean;
  currentRequestId: string | null;
  progress: number;

  // Usage tracking
  usageStats: UsageStats;
  healthStatus: Map<string, HealthStatus>;

  // Error handling
  error: string | null;
  lastError: Date | null;
  retryCount: number;

  // UI state
  showChatPanel: boolean;
  showProviderSettings: boolean;
  showUsageStats: boolean;
  chatPanelWidth: number;

  // Actions
  // Provider management
  setProviders: (providers: AIProvider[]) => void;
  addProvider: (provider: AIProvider) => void;
  updateProvider: (id: string, updates: Partial<AIProvider>) => void;
  removeProvider: (id: string) => void;
  setActiveProvider: (id: string) => void;

  // Model management
  setAvailableModels: (models: AIModel[]) => void;
  setSelectedModel: (modelId: string) => void;

  // Session management
  createSession: (title?: string, context?: Partial<AIContext>) => string;
  setCurrentSession: (sessionId: string) => void;
  updateSession: (sessionId: string, updates: Partial<ChatSession>) => void;
  deleteSession: (sessionId: string) => void;
  clearAllSessions: () => void;

  // Message management
  addMessage: (sessionId: string, message: Omit<ChatMessage, 'timestamp'>) => void;
  updateMessage: (sessionId: string, messageIndex: number, updates: Partial<ChatMessage>) => void;
  deleteMessage: (sessionId: string, messageIndex: number) => void;
  clearMessages: (sessionId: string) => void;

  // Request management
  submitRequest: (request: Omit<AIRequest, 'id' | 'metadata'>) => string;
  cancelRequest: (requestId: string) => void;
  retryRequest: (requestId: string) => void;
  clearRequestQueue: () => void;

  // Response management
  addResponse: (response: AIResponse) => void;
  getCachedResponse: (requestHash: string) => AIResponse | null;
  clearResponseCache: () => void;

  // Context management
  setContext: (context: AIContext) => void;
  updateContext: (updates: Partial<AIContext>) => void;
  clearContext: () => void;

  // Preferences
  updatePreferences: (preferences: Partial<UserPreferences>) => void;

  // Processing state
  setProcessing: (processing: boolean) => void;
  setGenerating: (generating: boolean) => void;
  setAnalyzing: (analyzing: boolean) => void;
  setCurrentRequest: (requestId: string | null) => void;
  setProgress: (progress: number) => void;

  // Usage tracking
  updateUsageStats: (stats: Partial<UsageStats>) => void;
  updateHealthStatus: (providerId: string, status: HealthStatus) => void;

  // Error handling
  setError: (error: string | null) => void;
  clearError: () => void;
  incrementRetryCount: () => void;
  resetRetryCount: () => void;

  // UI state
  setShowChatPanel: (show: boolean) => void;
  setShowProviderSettings: (show: boolean) => void;
  setShowUsageStats: (show: boolean) => void;
  setChatPanelWidth: (width: number) => void;

  // Utility actions
  reset: () => void;
  getActiveProvider: () => AIProvider | null;
  getCurrentSession: () => ChatSession | null;
  getSessionMessages: (sessionId: string) => ChatMessage[];
}

const initialUserPreferences: UserPreferences = {
  preferredModel: '',
  temperature: 0.7,
  maxTokens: 2048,
  language: 'en',
  responseStyle: 'detailed' as any,
};

const initialUsageStats: UsageStats = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  averageLatency: 0,
  totalTokensUsed: 0,
  totalCost: 0,
  period: {
    start: new Date(),
    end: new Date(),
    duration: 0,
  },
};

const initialState = {
  // Provider and model management
  providers: [],
  activeProviderId: null,
  selectedModel: null,
  availableModels: [],

  // Chat and conversation management
  activeSessions: [],
  currentSessionId: null,
  conversationHistory: new Map<string, ChatMessage[]>(),

  // Request and response management
  pendingRequests: new Map<string, AIRequest>(),
  responseCache: new Map<string, AIResponse>(),
  requestQueue: [],

  // AI context and preferences
  currentContext: null,
  userPreferences: initialUserPreferences,

  // Processing state
  isProcessing: false,
  isGenerating: false,
  isAnalyzing: false,
  currentRequestId: null,
  progress: 0,

  // Usage tracking
  usageStats: initialUsageStats,
  healthStatus: new Map<string, HealthStatus>(),

  // Error handling
  error: null,
  lastError: null,
  retryCount: 0,

  // UI state
  showChatPanel: false,
  showProviderSettings: false,
  showUsageStats: false,
  chatPanelWidth: 400,
};

export const useAIStore: any = create<AIState>()(
  subscribeWithSelector(
    immer<AIState>((set, _get) => ({
      ...initialState,

      // Provider management
      setProviders: (providers: AIProvider[]) =>
        set(state => {
          state.providers = providers;
          // Set first active provider as default if none selected
          if (!state.activeProviderId && providers.length > 0) {
            const activeProvider = providers.find((p: AIProvider) => p.isActive) || providers[0];
            if (activeProvider) {
              state.activeProviderId = activeProvider.id;
              state.availableModels = activeProvider.models;
              if (!state.selectedModel && activeProvider.models.length > 0) {
                const firstModel = activeProvider.models[0];
                if (firstModel) {
                  state.selectedModel = firstModel.id;
                }
              }
            }
          }
        }),

      addProvider: (provider: AIProvider) =>
        set(state => {
          state.providers.push(provider);
          if (!state.activeProviderId && provider.isActive) {
            state.activeProviderId = provider.id;
            state.availableModels = provider.models;
          }
        }),

      updateProvider: (id: string, updates: Partial<AIProvider>) =>
        set(state => {
          const index = state.providers.findIndex((p: AIProvider) => p.id === id);
          if (index !== -1) {
            state.providers[index] = { ...state.providers[index], ...updates };
            if (state.activeProviderId === id) {
              state.availableModels = state.providers[index].models;
            }
          }
        }),

      removeProvider: (id: string) =>
        set(state => {
          state.providers = state.providers.filter((p: AIProvider) => p.id !== id);
          if (state.activeProviderId === id) {
            const newActiveProvider =
              state.providers.find((p: AIProvider) => p.isActive) || state.providers[0];
            state.activeProviderId = newActiveProvider?.id || null;
            state.availableModels = newActiveProvider?.models || [];
            state.selectedModel = newActiveProvider?.models[0]?.id || null;
          }
        }),

      setActiveProvider: (id: string) =>
        set(state => {
          const provider = state.providers.find((p: AIProvider) => p.id === id);
          if (provider) {
            state.activeProviderId = id;
            state.availableModels = provider.models;
            // Reset selected model if not available in new provider
            if (!provider.models.find((m: AIModel) => m.id === state.selectedModel)) {
              state.selectedModel = provider.models[0]?.id || null;
            }
          }
        }),

      // Model management
      setAvailableModels: (models: AIModel[]) =>
        set(state => {
          state.availableModels = models;
          // Reset selected model if not in new list
          if (!models.find(m => m.id === state.selectedModel)) {
            state.selectedModel = models[0]?.id || null;
          }
        }),

      setSelectedModel: (modelId: string) =>
        set(state => {
          if (state.availableModels.find((m: AIModel) => m.id === modelId)) {
            state.selectedModel = modelId;
          }
        }),

      // Session management
      createSession: (title?: string, context?: Partial<AIContext>) => {
        const sessionId = uuid();
        const now = new Date();

        set(state => {
          const newSession: ChatSession = {
            id: sessionId,
            userId: context?.userId || 'default',
            title: title || `Chat ${now.toLocaleTimeString()}`,
            messages: [],
            createdAt: now,
            updatedAt: now,
            isActive: true,
            context: context ? { ...state.currentContext, ...context } : state.currentContext,
          };

          // Deactivate other sessions
          state.activeSessions.forEach((session: AISession) => {
            session.isActive = false;
          });

          state.activeSessions.push(newSession);
          state.currentSessionId = sessionId;
          state.conversationHistory.set(sessionId, []);
        });

        return sessionId;
      },

      setCurrentSession: (sessionId: string) =>
        set(state => {
          const session = state.activeSessions.find((s: AISession) => s.id === sessionId);
          if (session) {
            // Deactivate all sessions
            state.activeSessions.forEach((s: AISession) => {
              s.isActive = false;
            });

            // Activate target session
            session.isActive = true;
            state.currentSessionId = sessionId;
          }
        }),

      updateSession: (sessionId: string, updates: Partial<ChatSession>) =>
        set(state => {
          const session = state.activeSessions.find((s: AISession) => s.id === sessionId);
          if (session) {
            Object.assign(session, updates);
            session.updatedAt = new Date();
          }
        }),

      deleteSession: (sessionId: string) =>
        set(state => {
          state.activeSessions = state.activeSessions.filter((s: AISession) => s.id !== sessionId);
          state.conversationHistory.delete(sessionId);

          if (state.currentSessionId === sessionId) {
            const remainingSession = state.activeSessions[0];
            if (remainingSession) {
              remainingSession.isActive = true;
              state.currentSessionId = remainingSession.id;
            } else {
              state.currentSessionId = null;
            }
          }
        }),

      clearAllSessions: () =>
        set(state => {
          state.activeSessions = [];
          state.currentSessionId = null;
          state.conversationHistory.clear();
        }),

      // Message management
      addMessage: (sessionId: string, message: Omit<ChatMessage, 'timestamp'>) =>
        set(state => {
          const messageWithTimestamp: ChatMessage = {
            ...message,
            timestamp: new Date(),
          };

          const messages = state.conversationHistory.get(sessionId) || [];
          messages.push(messageWithTimestamp);
          state.conversationHistory.set(sessionId, messages);

          // Update session
          const session = state.activeSessions.find((s: AISession) => s.id === sessionId);
          if (session) {
            session.messages = messages;
            session.updatedAt = new Date();
          }
        }),

      updateMessage: (sessionId: string, messageIndex: number, updates: Partial<ChatMessage>) =>
        set(state => {
          const messages = state.conversationHistory.get(sessionId);
          if (messages && messages[messageIndex]) {
            Object.assign(messages[messageIndex], updates);
            state.conversationHistory.set(sessionId, messages);

            // Update session
            const session = state.activeSessions.find((s: AISession) => s.id === sessionId);
            if (session) {
              session.messages = messages;
              session.updatedAt = new Date();
            }
          }
        }),

      deleteMessage: (sessionId: string, messageIndex: number) =>
        set(state => {
          const messages = state.conversationHistory.get(sessionId);
          if (messages && messages[messageIndex]) {
            messages.splice(messageIndex, 1);
            state.conversationHistory.set(sessionId, messages);

            // Update session
            const session = state.activeSessions.find((s: AISession) => s.id === sessionId);
            if (session) {
              session.messages = messages;
              session.updatedAt = new Date();
            }
          }
        }),

      clearMessages: (sessionId: string) =>
        set(state => {
          state.conversationHistory.set(sessionId, []);

          // Update session
          const session = state.activeSessions.find((s: AISession) => s.id === sessionId);
          if (session) {
            session.messages = [];
            session.updatedAt = new Date();
          }
        }),

      // Request management
      submitRequest: (requestData: Omit<AIRequest, 'id' | 'metadata'>) => {
        const requestId = uuid();
        const now = new Date();

        set(state => {
          const request: AIRequest = {
            ...requestData,
            id: requestId,
            metadata: {
              userId: state.currentContext?.userId,
              sessionId: state.currentSessionId || undefined,
              documentId: state.currentContext?.documentIds[0],
              timestamp: now,
              priority: RequestPriority.NORMAL,
            },
          };

          state.pendingRequests.set(requestId, request);
          state.requestQueue.push(request);
          state.currentRequestId = requestId;
        });

        return requestId;
      },

      cancelRequest: (requestId: string) =>
        set(state => {
          state.pendingRequests.delete(requestId);
          state.requestQueue = state.requestQueue.filter((r: AIRequest) => r.id !== requestId);

          if (state.currentRequestId === requestId) {
            state.currentRequestId = null;
            state.isProcessing = false;
            state.isGenerating = false;
            state.isAnalyzing = false;
            state.progress = 0;
          }
        }),

      retryRequest: (requestId: string) =>
        set(state => {
          const request = state.pendingRequests.get(requestId);
          if (request) {
            // Update timestamp and add back to queue
            request.metadata.timestamp = new Date();
            state.requestQueue.push(request);
            state.retryCount += 1;
          }
        }),

      clearRequestQueue: () =>
        set(state => {
          state.requestQueue = [];
          state.pendingRequests.clear();
          state.currentRequestId = null;
          state.isProcessing = false;
          state.isGenerating = false;
          state.isAnalyzing = false;
          state.progress = 0;
        }),

      // Response management
      addResponse: (response: AIResponse) =>
        set(state => {
          // Cache the response
          const cacheKey = `${response.requestId}-${response.model}`;
          state.responseCache.set(cacheKey, response);

          // Remove from pending requests
          state.pendingRequests.delete(response.requestId);

          // Update usage stats
          state.usageStats.totalRequests += 1;
          if (response.success) {
            state.usageStats.successfulRequests += 1;
          } else {
            state.usageStats.failedRequests += 1;
          }
          state.usageStats.totalTokensUsed += response.tokensUsed;
          state.usageStats.totalCost += response.metadata.cost;

          // Update average latency
          const totalLatency =
            state.usageStats.averageLatency * (state.usageStats.totalRequests - 1) +
            response.processingTime;
          state.usageStats.averageLatency = totalLatency / state.usageStats.totalRequests;

          // Clear processing state if this was the current request
          if (state.currentRequestId === response.requestId) {
            state.currentRequestId = null;
            state.isProcessing = false;
            state.isGenerating = false;
            state.isAnalyzing = false;
            state.progress = 100;
          }
        }),

      getCachedResponse: (requestHash: string): AIResponse | null => {
        const state = useAIStore.getState();
        return state.responseCache.get(requestHash) || null;
      },

      clearResponseCache: () =>
        set(state => {
          state.responseCache.clear();
        }),

      // Context management
      setContext: (context: AIContext) =>
        set(state => {
          state.currentContext = context;
        }),

      updateContext: (updates: Partial<AIContext>) =>
        set(state => {
          if (state.currentContext) {
            state.currentContext = { ...state.currentContext, ...updates };
          }
        }),

      clearContext: () =>
        set(state => {
          state.currentContext = null;
        }),

      // Preferences
      updatePreferences: (preferences: Partial<UserPreferences>) =>
        set(state => {
          state.userPreferences = { ...state.userPreferences, ...preferences };
        }),

      // Processing state
      setProcessing: (processing: boolean) =>
        set(state => {
          state.isProcessing = processing;
        }),

      setGenerating: (generating: boolean) =>
        set(state => {
          state.isGenerating = generating;
        }),

      setAnalyzing: (analyzing: boolean) =>
        set(state => {
          state.isAnalyzing = analyzing;
        }),

      setCurrentRequest: (requestId: string | null) =>
        set(state => {
          state.currentRequestId = requestId;
        }),

      setProgress: (progress: number) =>
        set(state => {
          state.progress = Math.max(0, Math.min(100, progress));
        }),

      // Usage tracking
      updateUsageStats: (stats: Partial<UsageStats>) =>
        set(state => {
          state.usageStats = { ...state.usageStats, ...stats };
        }),

      updateHealthStatus: (providerId: string, status: HealthStatus) =>
        set(state => {
          state.healthStatus.set(providerId, status);
        }),

      // Error handling
      setError: (error: string | null) =>
        set(state => {
          state.error = error;
          if (error) {
            state.lastError = new Date();
          }
        }),

      clearError: () =>
        set(state => {
          state.error = null;
        }),

      incrementRetryCount: () =>
        set(state => {
          state.retryCount += 1;
        }),

      resetRetryCount: () =>
        set(state => {
          state.retryCount = 0;
        }),

      // UI state
      setShowChatPanel: (show: boolean) =>
        set(state => {
          state.showChatPanel = show;
        }),

      setShowProviderSettings: (show: boolean) =>
        set(state => {
          state.showProviderSettings = show;
        }),

      setShowUsageStats: (show: boolean) =>
        set(state => {
          state.showUsageStats = show;
        }),

      setChatPanelWidth: (width: number) =>
        set(state => {
          state.chatPanelWidth = Math.max(200, Math.min(800, width));
        }),

      // Utility actions
      reset: () => set(() => ({ ...initialState })),

      getActiveProvider: (): any => {
        const state: any = useAIStore.getState();
        return state.providers.find((p: AIProvider) => p.id === state.activeProviderId) || null;
      },

      getCurrentSession: () => {
        const state = useAIStore.getState();
        return state.activeSessions.find((s: AISession) => s.id === state.currentSessionId) || null;
      },

      getSessionMessages: (sessionId: string) => {
        const state = useAIStore.getState();
        return state.conversationHistory.get(sessionId) || [];
      },
    }))
  )
);

// Selectors for computed values
export const useAISelectors = () => {
  const store = useAIStore();

  return {
    // Active provider with health status
    activeProviderWithHealth: () => {
      const provider = store.providers.find((p: AIProvider) => p.id === store.activeProviderId);
      if (!provider) return null;

      const health = store.healthStatus.get(provider.id);
      return { provider, health };
    },

    // Available models for current provider
    availableModelsForProvider: () => {
      const provider = store.providers.find((p: AIProvider) => p.id === store.activeProviderId);
      return provider?.models || [];
    },

    // Current session with messages
    currentSessionWithMessages: () => {
      const session = store.activeSessions.find((s: AISession) => s.id === store.currentSessionId);
      if (!session) return null;

      const messages = store.conversationHistory.get(session.id) || [];
      return { session, messages };
    },

    // Recent sessions
    recentSessions: (limit = 10) => {
      return store.activeSessions
        .sort((a: any, b: any) => b.updatedAt.getTime() - a.updatedAt.getTime())
        .slice(0, limit);
    },

    // Processing status
    processingStatus: () => {
      return {
        isAnyProcessing: store.isProcessing || store.isGenerating || store.isAnalyzing,
        currentActivity: store.isProcessing
          ? 'processing'
          : store.isGenerating
            ? 'generating'
            : store.isAnalyzing
              ? 'analyzing'
              : 'idle',
        progress: store.progress,
        currentRequestId: store.currentRequestId,
        queueLength: store.requestQueue.length,
      };
    },

    // Usage statistics summary
    usageStatsSummary: () => {
      const stats = store.usageStats;
      const successRate =
        stats.totalRequests > 0 ? (stats.successfulRequests / stats.totalRequests) * 100 : 0;

      return {
        ...stats,
        successRate,
        averageCostPerRequest: stats.totalRequests > 0 ? stats.totalCost / stats.totalRequests : 0,
        averageTokensPerRequest:
          stats.totalRequests > 0 ? stats.totalTokensUsed / stats.totalRequests : 0,
      };
    },

    // Provider health summary
    providerHealthSummary: () => {
      const healthyProviders = Array.from(store.healthStatus.values()).filter(
        ((status: ProviderHealthStatus) => status.isHealthy) as any
      ).length;
      const totalProviders = store.providers.length;

      return {
        healthyCount: healthyProviders,
        totalCount: totalProviders,
        healthyPercentage: totalProviders > 0 ? (healthyProviders / totalProviders) * 100 : 0,
        unhealthyProviders: store.providers.filter((p: AIProvider) => {
          const health = store.healthStatus.get(p.id);
          return health && !health.isHealthy;
        }),
      };
    },

    // Cache statistics
    cacheStats: () => {
      return {
        totalCachedResponses: store.responseCache.size,
        cacheHitRate: 0, // Would need to track cache hits vs misses
        oldestCacheEntry: null, // Would need to track cache timestamps
        cacheSize: store.responseCache.size,
      };
    },

    // Error summary
    errorSummary: () => {
      return {
        hasError: !!store.error,
        currentError: store.error,
        lastErrorTime: store.lastError,
        retryCount: store.retryCount,
        canRetry: store.retryCount < 3, // Max 3 retries
      };
    },
  };
};
