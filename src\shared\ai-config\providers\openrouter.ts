import { Anthropic } from '@anthropic-ai/sdk';
import OpenA<PERSON> from 'openai';
import { ApiHandler } from '../';
import { ModelInfo, openRouterDefaultModelId, openRouterDefaultModelInfo } from '../api';
import { withRetry } from '../retry';
import { createOpenRouterStream } from '../transform/openrouter-stream';
import type { ApiStream, ApiStreamUsageChunk } from '../transform/stream';
import { OpenRouterErrorResponse } from './types';
import { shouldSkipReasoningForModel } from '../../utils/model-utils';

interface OpenRouterHandlerOptions {
  openRouterApiKey?: string;
  openRouterModelId?: string;
  openRouterModelInfo?: ModelInfo;
  openRouterProviderSorting?: string;
  reasoningEffort?: string;
  thinkingBudgetTokens?: number;
}

interface OpenRouterHandlerOptions {
  openRouterApiKey?: string;
  openRouterModelId?: string;
  openRouterModelInfo?: ModelInfo;
  openRouterProviderSorting?: string;
  reasoningEffort?: string;
  thinkingBudgetTokens?: number;
}

export class OpenRouterHandler implements ApiHandler {
  private options: OpenRouterHandlerOptions;
  private client: OpenAI | undefined;
  lastGenerationId?: string;

  constructor(options: OpenRouterHandlerOptions) {
    this.options = options;
  }

  private ensureClient(): OpenAI {
    if (!this.client) {
      if (!this.options.openRouterApiKey) {
        throw new Error('OpenRouter API key is required');
      }
      try {
        this.client = new OpenAI({
          baseURL: 'https://openrouter.ai/api/v1',
          apiKey: this.options.openRouterApiKey,
          defaultHeaders: {
            'HTTP-Referer': 'https://cline.bot', // Optional, for including your app on openrouter.ai rankings.
            'X-Title': 'Cline', // Optional. Shows in rankings on openrouter.ai.
          },
        });
      } catch (error: any) {
        throw new Error(`Error creating OpenRouter client: ${error.message}`);
      }
    }
    return this.client;
  }

  @withRetry()
  async *createMessage(
    systemPrompt: string,
    messages: Anthropic.Messages.MessageParam[]
  ): ApiStream {
    const client = this.ensureClient();
    delete this.lastGenerationId;

    const stream = await createOpenRouterStream(
      client,
      systemPrompt,
      messages,
      this.getModel(),
      this.options.reasoningEffort,
      this.options.thinkingBudgetTokens,
      this.options.openRouterProviderSorting
    );

    let didOutputUsage: boolean = false;

    for await (const chunk of stream) {
      // openrouter returns an error object instead of the openai sdk throwing an error
      // Check for error field directly on chunk
      if ('error' in chunk) {
        const error = chunk.error as OpenRouterErrorResponse['error'];
        console.error(`OpenRouter API Error: ${error?.code} - ${error?.message}`);
        // Include metadata in the error message if available
        const metadataStr = error.metadata
          ? `\nMetadata: ${JSON.stringify(error.metadata, null, 2)}`
          : '';
        throw new Error(`OpenRouter API Error ${error.code}: ${error.message}${metadataStr}`);
      }

      // Check for error in choices[0].finish_reason
      // OpenRouter may return errors in a non-standard way within choices
      const choice = chunk.choices?.[0];
      // Use type assertion since OpenRouter uses non-standard "error" finish_reason
      if ((choice?.finish_reason as string) === 'error') {
        // Use type assertion since OpenRouter adds non-standard error property
        const choiceWithError = choice as any;
        if (choiceWithError.error) {
          const error = choiceWithError.error;
          console.error(
            `OpenRouter Mid-Stream Error: ${error?.code || 'Unknown'} - ${error?.message || 'Unknown error'}`
          );
          // Format error details
          const errorDetails =
            typeof error === 'object' ? JSON.stringify(error, null, 2) : String(error);
          throw new Error(`OpenRouter Mid-Stream Error: ${errorDetails}`);
        } else {
          // Fallback if error details are not available
          throw new Error(
            `OpenRouter Mid-Stream Error: Stream terminated with error status but no error details provided`
          );
        }
      }

      if (!this.lastGenerationId && chunk.id) {
        this.lastGenerationId = chunk.id;
      }

      const delta = chunk.choices[0]?.delta;
      if (delta?.content) {
        yield {
          type: 'text',
          text: delta.content,
        };
      }

      // Reasoning tokens are returned separately from the content
      // Skip reasoning content for Grok 4 models since it only displays "thinking" without providing useful information
      if (
        delta &&
        'reasoning' in delta &&
        delta.reasoning &&
        !shouldSkipReasoningForModel(this.options.openRouterModelId)
      ) {
        yield {
          type: 'reasoning',
          // @ts-ignore-next-line
          reasoning: delta.reasoning,
        };
      }

      if (!didOutputUsage && chunk.usage) {
        const modelId = this.options.openRouterModelId;
        if (modelId && modelId.includes('gemini')) {
          yield {
            type: 'usage',
            cacheWriteTokens: 0,
            cacheReadTokens: chunk.usage.prompt_tokens_details?.cached_tokens || 0,
            inputTokens:
              (chunk.usage.prompt_tokens || 0) -
              (chunk.usage.prompt_tokens_details?.cached_tokens || 0),
            outputTokens: chunk.usage.completion_tokens || 0,
            // @ts-ignore-next-line
            totalCost:
              ((chunk.usage as any).cost || 0) +
              ((chunk.usage as any).cost_details?.upstream_inference_cost || 0),
          };
        } else {
          yield {
            type: 'usage',
            cacheWriteTokens: 0,
            cacheReadTokens: chunk.usage.prompt_tokens_details?.cached_tokens || 0,
            inputTokens: chunk.usage.prompt_tokens || 0,
            outputTokens: chunk.usage.completion_tokens || 0,
            // @ts-ignore-next-line
            totalCost:
              ((chunk.usage as any).cost || 0) +
              ((chunk.usage as any).cost_details?.upstream_inference_cost || 0),
          };
        }
        didOutputUsage = true;
      }
    }

    // Fallback to generation endpoint if usage chunk not returned
    if (!didOutputUsage) {
      const apiStreamUsage = await this.getApiStreamUsage();
      if (apiStreamUsage) {
        yield apiStreamUsage;
      }
    }
  }

  async getApiStreamUsage(): Promise<ApiStreamUsageChunk | undefined> {
    return Promise.resolve(undefined);
  }

  *fetchGenerationDetails(_genId: string) {
    yield null;
  }

  getModel(): { id: string; info: ModelInfo } {
    const modelId = this.options.openRouterModelId;
    const modelInfo = this.options.openRouterModelInfo;
    if (modelId && modelInfo) {
      return { id: modelId, info: modelInfo };
    }
    return { id: openRouterDefaultModelId, info: openRouterDefaultModelInfo };
  }
}
