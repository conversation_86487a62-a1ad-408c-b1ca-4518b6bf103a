import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { v4 as uuid } from 'uuid';
import {
  Tab,
  TabType,
  TabGroup,
  TabSession,
  ClosedTab,
  TabManagerSettings,
  TabWidth,
} from '../../shared/types/UI';

export interface TabState {
  // Core tab data
  tabs: Tab[];
  groups: TabGroup[];
  activeTabId: string | null;
  maxTabs: number;

  // Tab history and recently closed
  tabHistory: string[];
  recentlyClosedTabs: ClosedTab[];

  // Session management
  currentSession: TabSession | null;
  savedSessions: TabSession[];
  autoSaveEnabled: boolean;

  // Settings
  settings: TabManagerSettings;

  // UI state
  isLoading: boolean;
  isDragging: boolean;
  draggedTabId: string | null;
  dropTargetIndex: number | null;

  // Error state
  error: string | null;

  // Actions
  // Tab operations
  openTab: (tab: Omit<Tab, 'id' | 'createdAt' | 'lastAccessedAt'>) => string;
  closeTab: (tabId: string) => void;
  activateTab: (tabId: string) => void;
  pinTab: (tabId: string) => void;
  unpinTab: (tabId: string) => void;
  duplicateTab: (tabId: string) => string | null;
  moveTab: (tabId: string, newIndex: number) => void;
  updateTab: (tabId: string, updates: Partial<Tab>) => void;

  // Group operations
  createGroup: (name: string, tabIds: string[]) => string;
  addToGroup: (tabId: string, groupId: string) => void;
  removeFromGroup: (tabId: string) => void;
  deleteGroup: (groupId: string) => void;
  updateGroup: (groupId: string, updates: Partial<TabGroup>) => void;
  collapseGroup: (groupId: string) => void;
  expandGroup: (groupId: string) => void;

  // Session management
  saveSession: (name?: string) => string;
  restoreSession: (sessionId: string) => void;
  deleteSession: (sessionId: string) => void;
  autoSaveSession: () => void;

  // Recently closed tabs
  restoreClosedTab: (index?: number) => string | null;
  clearRecentlyClosedTabs: () => void;

  // Drag and drop
  startDrag: (tabId: string) => void;
  endDrag: () => void;
  setDropTarget: (index: number | null) => void;

  // Settings
  updateSettings: (settings: Partial<TabManagerSettings>) => void;

  // UI state
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;

  // Utility actions
  reset: () => void;
  getTab: (id: string) => Tab | undefined;
  getTabsByGroup: (groupId: string) => Tab[];
  getTabsByType: (type: TabType) => Tab[];
  hasUnsavedTabs: () => boolean;
}

const initialSettings: TabManagerSettings = {
  autoSave: true,
  maxRecentlyClosedTabs: 10,
  tabCloseConfirmation: true,
  groupTabsByType: false,
  showTabIcons: true,
  showTabTooltips: true,
  tabWidth: TabWidth.NORMAL,
};

const initialState = {
  // Core tab data
  tabs: [],
  groups: [],
  activeTabId: null,
  maxTabs: 20,

  // Tab history and recently closed
  tabHistory: [],
  recentlyClosedTabs: [],

  // Session management
  currentSession: null,
  savedSessions: [],
  autoSaveEnabled: true,

  // Settings
  settings: initialSettings,

  // UI state
  isLoading: false,
  isDragging: false,
  draggedTabId: null,
  dropTargetIndex: null,

  // Error state
  error: null,
};

export const useTabStore: any = create<TabState>()(
  subscribeWithSelector(
    immer<TabState>((set, get) => ({
      ...initialState,

      // Tab operations
      openTab: (tabData: Omit<Tab, 'id' | 'createdAt' | 'lastAccessedAt'>) => {
        const tabId = uuid();
        const now = new Date();

        set(state => {
          // Check tab limit
          if (state.tabs.length >= state.maxTabs) {
            // Close oldest unpinned tab
            const oldestUnpinnedTab = state.tabs
              .filter((tab: Tab) => !tab.isPinned)
              .sort((a: Tab, b: Tab) => a.lastAccessedAt.getTime() - b.lastAccessedAt.getTime())[0];

            if (oldestUnpinnedTab) {
              // Move to recently closed
              state.recentlyClosedTabs.unshift({
                tab: oldestUnpinnedTab,
                closedAt: now,
                position: state.tabs.findIndex((t: Tab) => t.id === oldestUnpinnedTab.id),
                groupId: oldestUnpinnedTab.groupId,
              });

              // Remove from tabs
              state.tabs = state.tabs.filter((t: Tab) => t.id !== oldestUnpinnedTab.id);

              // Update history
              state.tabHistory = state.tabHistory.filter(
                (id: string) => id !== oldestUnpinnedTab.id
              );
            }
          }

          const newTab: Tab = {
            ...tabData,
            id: tabId,
            isActive: false,
            createdAt: now,
            lastAccessedAt: now,
          };

          // Deactivate all other tabs
          state.tabs.forEach((tab: Tab) => {
            tab.isActive = false;
          });

          // Add new tab
          newTab.isActive = true;
          state.tabs.push(newTab);
          state.activeTabId = tabId;

          // Update history
          state.tabHistory = [
            tabId,
            ...state.tabHistory.filter((id: string) => id !== tabId),
          ].slice(0, 50);
        });

        return tabId;
      },

      closeTab: (tabId: string) =>
        set(state => {
          const tabIndex = state.tabs.findIndex((t: Tab) => t.id === tabId);
          if (tabIndex === -1) return;

          const tab = state.tabs[tabIndex];
          const now = new Date();

          // Add to recently closed
          state.recentlyClosedTabs.unshift({
            tab,
            closedAt: now,
            position: tabIndex,
            groupId: tab.groupId,
          });

          // Keep only max recently closed tabs
          if (state.recentlyClosedTabs.length > state.settings.maxRecentlyClosedTabs) {
            state.recentlyClosedTabs = state.recentlyClosedTabs.slice(
              0,
              state.settings.maxRecentlyClosedTabs
            );
          }

          // Remove from tabs
          state.tabs.splice(tabIndex, 1);

          // Update history
          state.tabHistory = state.tabHistory.filter((id: string) => id !== tabId);

          // Handle active tab change
          if (state.activeTabId === tabId) {
            if (state.tabs.length > 0) {
              // Activate the next tab or previous if at end
              const newActiveIndex = Math.min(tabIndex, state.tabs.length - 1);
              const newActiveTab = state.tabs[newActiveIndex];
              if (newActiveTab) {
                newActiveTab.isActive = true;
                state.activeTabId = newActiveTab.id;
              }
            } else {
              state.activeTabId = null;
            }
          }
        }),

      activateTab: (tabId: string) =>
        set(state => {
          const tab = state.tabs.find((t: Tab) => t.id === tabId);
          if (!tab) return;

          // Deactivate all tabs
          state.tabs.forEach((t: Tab) => {
            t.isActive = false;
          });

          // Activate target tab
          tab.isActive = true;
          tab.lastAccessedAt = new Date();
          state.activeTabId = tabId;

          // Update history
          state.tabHistory = [
            tabId,
            ...state.tabHistory.filter((id: string) => id !== tabId),
          ].slice(0, 50);
        }),

      pinTab: (tabId: string) =>
        set(state => {
          const tab = state.tabs.find((t: Tab) => t.id === tabId);
          if (tab) {
            tab.isPinned = true;
          }
        }),

      unpinTab: (tabId: string) =>
        set(state => {
          const tab = state.tabs.find((t: Tab) => t.id === tabId);
          if (tab) {
            tab.isPinned = false;
          }
        }),

      duplicateTab: (tabId: string): string => {
        const state = get();
        const tab = state.tabs.find((t: Tab) => t.id === tabId);
        if (!tab) return '';

        const duplicatedTab = {
          ...tab,
          title: `${tab.title} (Copy)`,
          isDirty: false,
          isPinned: false,
        };

        return state.openTab(duplicatedTab);
      },

      moveTab: (tabId: string, newIndex: number) =>
        set(state => {
          const currentIndex = state.tabs.findIndex((t: Tab) => t.id === tabId);
          if (currentIndex === -1 || currentIndex === newIndex) return;

          const [tab] = state.tabs.splice(currentIndex, 1);
          state.tabs.splice(newIndex, 0, tab);
        }),

      updateTab: (tabId: string, updates: Partial<Tab>) =>
        set(state => {
          const tab = state.tabs.find((t: Tab) => t.id === tabId);
          if (tab) {
            Object.assign(tab, updates);
          }
        }),

      // Group operations
      createGroup: (name: string, tabIds: string[]) => {
        const groupId = uuid();

        set(state => {
          const newGroup: TabGroup = {
            id: groupId,
            name,
            color: `hsl(${Math.floor(Math.random() * 360)}, 70%, 50%)`,
            tabs: tabIds,
            isCollapsed: false,
            position: state.groups.length,
            metadata: {
              tags: [],
              createdAt: new Date(),
              lastModified: new Date(),
              customProperties: {},
            },
          };

          state.groups.push(newGroup);

          // Update tabs to reference the group
          tabIds.forEach(tabId => {
            const tab = state.tabs.find((t: Tab) => t.id === tabId);
            if (tab) {
              tab.groupId = groupId;
            }
          });
        });

        return groupId;
      },

      addToGroup: (tabId: string, groupId: string) =>
        set(state => {
          const tab = state.tabs.find((t: Tab) => t.id === tabId);
          const group = state.groups.find((g: TabGroup) => g.id === groupId);

          if (tab && group) {
            // Remove from current group if any
            if (tab.groupId) {
              const currentGroup = state.groups.find((g: TabGroup) => g.id === tab.groupId);
              if (currentGroup) {
                currentGroup.tabs = currentGroup.tabs.filter((id: string) => id !== tabId);
              }
            }

            // Add to new group
            tab.groupId = groupId;
            if (!group.tabs.includes(tabId)) {
              group.tabs.push(tabId);
            }
            group.metadata.lastModified = new Date();
          }
        }),

      removeFromGroup: (tabId: string) =>
        set(state => {
          const tab = state.tabs.find((t: Tab) => t.id === tabId);
          if (tab && tab.groupId) {
            const group = state.groups.find((g: TabGroup) => g.id === tab.groupId);
            if (group) {
              group.tabs = group.tabs.filter((id: string) => id !== tabId);
              group.metadata.lastModified = new Date();
            }
            tab.groupId = undefined;
          }
        }),

      deleteGroup: (groupId: string) =>
        set(state => {
          const group = state.groups.find((g: TabGroup) => g.id === groupId);
          if (group) {
            // Remove group reference from tabs
            group.tabs.forEach((tabId: string) => {
              const tab = state.tabs.find((t: Tab) => t.id === tabId);
              if (tab) {
                tab.groupId = undefined;
              }
            });

            // Remove group
            state.groups = state.groups.filter((g: TabGroup) => g.id !== groupId);
          }
        }),

      updateGroup: (groupId: string, updates: Partial<TabGroup>) =>
        set(state => {
          const group = state.groups.find((g: TabGroup) => g.id === groupId);
          if (group) {
            Object.assign(group, updates);
            group.metadata.lastModified = new Date();
          }
        }),

      collapseGroup: (groupId: string) =>
        set(state => {
          const group = state.groups.find((g: TabGroup) => g.id === groupId);
          if (group) {
            group.isCollapsed = true;
            group.metadata.lastModified = new Date();
          }
        }),

      expandGroup: (groupId: string) =>
        set(state => {
          const group = state.groups.find((g: TabGroup) => g.id === groupId);
          if (group) {
            group.isCollapsed = false;
            group.metadata.lastModified = new Date();
          }
        }),

      // Session management
      saveSession: (name?: string) => {
        const sessionId = uuid();
        const now = new Date();

        set(state => {
          const session: TabSession = {
            id: sessionId,
            name: name || `Session ${now.toLocaleString()}`,
            tabs: [...state.tabs],
            groups: [...state.groups],
            activeTabId: state.activeTabId,
            createdAt: now,
            lastModified: now,
            metadata: {
              tags: [],
              isAutoSaved: !name,
              totalTabs: state.tabs.length,
              totalGroups: state.groups.length,
            },
          };

          state.savedSessions.push(session);
          state.currentSession = session;
        });

        return sessionId;
      },

      restoreSession: (sessionId: string) =>
        set(state => {
          const session = state.savedSessions.find((s: TabSession) => s.id === sessionId);
          if (session) {
            state.tabs = [...session.tabs];
            state.groups = [...session.groups];
            state.activeTabId = session.activeTabId;
            state.currentSession = session;

            // Update tab history
            state.tabHistory = session.tabs.map((tab: Tab) => tab.id);
          }
        }),

      deleteSession: (sessionId: string) =>
        set(state => {
          state.savedSessions = state.savedSessions.filter((s: TabSession) => s.id !== sessionId);
          if (state.currentSession?.id === sessionId) {
            state.currentSession = null;
          }
        }),

      autoSaveSession: () => {
        const state = useTabStore.getState();
        if (state.autoSaveEnabled && state.tabs.length > 0) {
          state.saveSession();
        }
      },

      // Recently closed tabs
      restoreClosedTab: (index = 0): any => {
        const state: any = useTabStore.getState();
        const closedTab = state.recentlyClosedTabs[index];
        if (!closedTab) return null;

        set(stateSet => {
          // Remove from recently closed
          stateSet.recentlyClosedTabs.splice(index, 1);

          // Restore tab
          const restoredTab = {
            ...closedTab.tab,
            id: uuid(), // New ID to avoid conflicts
            isActive: false,
            lastAccessedAt: new Date(),
          };

          // Insert at original position or end
          const insertIndex = Math.min(closedTab.position, stateSet.tabs.length);
          stateSet.tabs.splice(insertIndex, 0, restoredTab);

          // Restore group if it exists
          if (closedTab.groupId) {
            const group = stateSet.groups.find((g: any) => g.id === closedTab.groupId);
            if (group) {
              restoredTab.groupId = closedTab.groupId;
              group.tabs.push(restoredTab.id);
            }
          }
        });

        return state.tabs[state.tabs.length - 1]?.id || null;
      },

      clearRecentlyClosedTabs: () =>
        set(state => {
          state.recentlyClosedTabs = [];
        }),

      // Drag and drop
      startDrag: (tabId: string) =>
        set(state => {
          state.isDragging = true;
          state.draggedTabId = tabId;
        }),

      endDrag: () =>
        set(state => {
          state.isDragging = false;
          state.draggedTabId = null;
          state.dropTargetIndex = null;
        }),

      setDropTarget: (index: number | null) =>
        set(state => {
          state.dropTargetIndex = index;
        }),

      // Settings
      updateSettings: (settings: Partial<TabManagerSettings>) =>
        set(state => {
          state.settings = { ...state.settings, ...settings };
        }),

      // UI state
      setLoading: (loading: boolean) =>
        set(state => {
          state.isLoading = loading;
        }),

      setError: (error: string | null) =>
        set(state => {
          state.error = error;
        }),

      clearError: () =>
        set(state => {
          state.error = null;
        }),

      // Utility actions
      reset: () => set(() => ({ ...initialState })),

      getTab: (id: string) => {
        const state = useTabStore.getState();
        return state.tabs.find((tab: any) => tab.id === id);
      },

      getTabsByGroup: (groupId: string) => {
        const state = useTabStore.getState();
        return state.tabs.filter((tab: any) => tab.groupId === groupId);
      },

      getTabsByType: (type: TabType) => {
        const state = useTabStore.getState();
        return state.tabs.filter((tab: Tab) => tab.type === type);
      },

      hasUnsavedTabs: () => {
        const state = useTabStore.getState();
        return state.tabs.some((tab: Tab) => tab.isDirty);
      },
    }))
  )
);

// Selectors for computed values
export const useTabSelectors = () => {
  const store = useTabStore();

  return {
    // Active tab
    activeTab: () => {
      return store.tabs.find((tab: Tab) => tab.id === store.activeTabId) || null;
    },

    // Pinned tabs
    pinnedTabs: () => {
      return store.tabs.filter((tab: Tab) => tab.isPinned);
    },

    // Unpinned tabs
    unpinnedTabs: () => {
      return store.tabs.filter((tab: Tab) => !tab.isPinned);
    },

    // Dirty tabs (unsaved changes)
    dirtyTabs: () => {
      return store.tabs.filter((tab: Tab) => tab.isDirty);
    },

    // Tabs by group
    tabsByGroup: () => {
      const grouped = new Map<string, Tab[]>();
      const ungrouped: Tab[] = [];

      store.tabs.forEach((tab: Tab) => {
        if (tab.groupId) {
          if (!grouped.has(tab.groupId)) {
            grouped.set(tab.groupId, []);
          }
          grouped.get(tab.groupId)!.push(tab);
        } else {
          ungrouped.push(tab);
        }
      });

      return { grouped, ungrouped };
    },

    // Tab statistics
    tabStats: () => {
      const totalTabs = store.tabs.length;
      const pinnedCount = store.tabs.filter((tab: Tab) => tab.isPinned).length;
      const dirtyCount = store.tabs.filter((tab: Tab) => tab.isDirty).length;
      const groupedCount = store.tabs.filter((tab: Tab) => tab.groupId).length;

      const tabsByType = store.tabs.reduce(
        (acc: Record<TabType, number>, tab: Tab) => {
          acc[tab.type] = (acc[tab.type] || 0) + 1;
          return acc;
        },
        {} as Record<TabType, number>
      );

      return {
        totalTabs,
        pinnedCount,
        dirtyCount,
        groupedCount,
        ungroupedCount: totalTabs - groupedCount,
        tabsByType,
        totalGroups: store.groups.length,
        recentlyClosedCount: store.recentlyClosedTabs.length,
        savedSessionsCount: store.savedSessions.length,
      };
    },

    // Recent tabs (from history)
    recentTabs: (limit = 10) => {
      return store.tabHistory
        .slice(0, limit)
        .map((id: string) => store.tabs.find((tab: Tab) => tab.id === id))
        .filter(Boolean) as Tab[];
    },

    // Can close tab (considering pinned status and dirty state)
    canCloseTab: (tabId: string) => {
      const tab = store.tabs.find((t: Tab) => t.id === tabId);
      if (!tab) return false;

      // Can always close if not pinned and not dirty
      if (!tab.isPinned && !tab.isDirty) return true;

      // If pinned or dirty, check settings
      if (tab.isPinned && !store.settings.tabCloseConfirmation) return false;
      if (tab.isDirty && store.settings.tabCloseConfirmation) return false;

      return true;
    },

    // Next tab in sequence
    nextTab: (currentTabId?: string) => {
      const tabId = currentTabId || store.activeTabId;
      if (!tabId) return null;

      const currentIndex = store.tabs.findIndex((tab: Tab) => tab.id === tabId);
      if (currentIndex === -1) return null;

      const nextIndex = (currentIndex + 1) % store.tabs.length;
      return store.tabs[nextIndex] || null;
    },

    // Previous tab in sequence
    previousTab: (currentTabId?: string) => {
      const tabId = currentTabId || store.activeTabId;
      if (!tabId) return null;

      const currentIndex = store.tabs.findIndex((tab: Tab) => tab.id === tabId);
      if (currentIndex === -1) return null;

      const prevIndex = currentIndex === 0 ? store.tabs.length - 1 : currentIndex - 1;
      return store.tabs[prevIndex] || null;
    },
  };
};
